# 香百年香薰机XH-008蓝牙通讯协议

## 1. 协议概述

### 1.1 基本信息
- **设备型号**: XH-008香薰机
- **通讯方式**: 蓝牙低功耗(BLE)
- **协议版本**: v3.0
- **文档版本**: 2025-01-31

### 1.2 服务和特征值

## 2. 协议格式

### 2.1 数据帧结构

#### 发送帧格式（APP → 设备）
```
| 帧头1 | 帧头2 | 命令 | 数据长度 | 数据 | CRC |
|-------|-------|------|----------|------|-----|
| 0xAA  | 0x55  | CMD  |   LEN    | DATA | CRC |
```

#### 响应帧格式（设备 → APP）
```
| 帧头1 | 帧头2 | 命令 | 数据长度 | 数据 | CRC |
|-------|-------|------|----------|------|-----|
| 0x55  | 0xAA  | CMD  |   LEN    | DATA | CRC |
```

### 2.2 CRC校验
- **算法**: 补码校验
- **计算方式**: `CRC = (0 - SUM(所有字节)) & 0xFF`
- **范围**: 从帧头到数据的所有字节

## 3. 命令定义

### 3.1 命令码表

| 命令码 | 命令名称 | 描述 |
|--------|----------|------|
| 0x00   | CMD_POWER | 电源控制 |
| 0x01   | CMD_SET_WORK_MODE | 设置工作模式 |
| 0x0B   | CMD_GET_STATUS | 获取设备状态 |

### 3.2 响应类型

| 响应码 | 响应名称 | 描述 |
|--------|----------|------|
| 0x0A   | RESP_FRAGRANCE_LEVEL | 香薰余量响应 |
| 0x0B   | RESP_BATTERY_STATUS | 电池状态响应 |

## 4. 数据类型定义

### 4.1 电源状态 (PowerState)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x00 | OFF | 关机 |
| 0x01 | ON | 开机 |
| 0xFF | UNKNOWN | 未知状态 |

### 4.2 工作模式 (WorkMode)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x00 | UNKNOWN | 未知模式 |
| 0x01 | AUTO | 自动模式 |
| 0x02 | CYCLE | 循环模式 |
| 0x03 | TIMER | 定时模式 |

### 4.3 香氛类型 (FragranceType)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x00 | NONE | 无香氛 |
| 0x01 | TEA_STORY | 茶语 |
| 0x02 | SANDALWOOD | 檀香 |
| 0x03 | DESERT_ROSE | 沙漠玫瑰 |
| 0x04 | BLUE_FLOWER_SEA | 蓝色花海 |

### 4.4 香氛组合 (FragranceCombo)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x01 | FRAGRANCE1 | 香氛1 |
| 0x02 | FRAGRANCE2 | 香氛2 |
| 0x03 | COMBO | 组合香氛 |

### 4.5 随车启停模式 (VehicleStartStopMode)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x00 | OFF | 关闭 |
| 0x01 | WORK_5_STOP_10 | 工作5分钟，停止10分钟 |
| 0x02 | WORK_5_STOP_20 | 工作5分钟，停止20分钟 |
| 0x03 | WORK_5_STOP_30 | 工作5分钟，停止30分钟 |

### 4.6 浓度等级 (IntensityLevel)

| 值 | 名称 | 描述 |
|----|------|------|
| 0x00 | STRONG | 强 |
| 0x01 | WEAK | 弱 |

## 5. 命令详细说明

### 5.1 电源控制命令 (0x00)

#### 发送格式
```
AA 55 00 01 [POWER_DATA] [CRC]
```

**参数说明**:
- `POWER_DATA`: 
  - `0x01`: 开机
  - `0x02`: 关机

#### 响应格式
```
55 AA 00 01 [POWER_STATE] [CRC]
```

**示例**:
- 开机命令: `AA 55 00 01 01 A9`
- 关机命令: `AA 55 00 01 02 A8`

### 5.2 设置工作模式命令 (0x01)

#### 发送格式
```
AA 55 01 01 [WORK_MODE] [CRC]
```

**参数说明**:
- `WORK_MODE`: 参考工作模式定义

#### 响应格式
```
55 AA 01 01 [WORK_MODE] [CRC]
```

### 5.3 获取设备状态命令 (0x0B)

#### 发送格式
```
AA 55 0B 01 01 [CRC]
```

#### 响应格式
```
55 AA 0B [LEN] [STATUS_DATA] [CRC]
```

**状态数据结构**:
```
| 字节位置 | 字段名称 | 描述 |
|----------|----------|------|
| 0 | 电源状态 | PowerState |
| 1 | 工作模式 | WorkMode |
| 2 | 循环次数 | 1-255 |
| 3 | 工作时长 | 分钟 |
| 4 | 香氛1类型 | FragranceType |
| 5 | 香氛2类型 | FragranceType |
| 6 | 香氛组合 | FragranceCombo |
| 7 | 自动连接 | 0x00/0x01 |
| 8 | 随车启停 | VehicleStartStopMode |
| 9 | 浓度等级 | IntensityLevel |
```

## 6. 设备状态数据结构

### 6.1 完整设备状态 (DeviceState)

```kotlin
data class DeviceState(
    val powerState: PowerState = PowerState.UNKNOWN,
    val workMode: WorkMode = WorkMode.UNKNOWN,
    val cycleCount: Int = 1,
    val workDuration: Int = 5, // 分钟
    val fragrance1Type: FragranceType = FragranceType.NONE,
    val fragrance2Type: FragranceType = FragranceType.NONE,
    val fragranceCombo: FragranceCombo = FragranceCombo.FRAGRANCE1,
    val autoConnect: Boolean = false,
    val vehicleStartStop: VehicleStartStopMode = VehicleStartStopMode.OFF,
    val intensity: IntensityLevel = IntensityLevel.WEAK,
    val fragrance1Level: Int = 0, // 0-100%
    val fragrance2Level: Int = 0, // 0-100%
    val batteryLevel: Int = 0, // 0-100%
    val isCharging: Boolean = false,
    val isFullyCharged: Boolean = false,
    val lastUpdateTime: Long = System.currentTimeMillis()
)
```

## 7. 异常处理

### 7.1 异常类型定义

| 异常类型 | 描述 |
|----------|------|
| ConnectionException | 连接异常 |
| WriteException | 写入异常 |
| InvalidResponseException | 无效响应异常 |
| CrcValidationException | CRC校验异常 |
| TimeoutException | 超时异常 |

### 7.2 常见错误处理

#### GATT错误133处理
- **原因**: 连接不稳定、设备忙碌、信号干扰
- **处理**: 延迟重试、清理GATT缓存、重新连接

#### 数据格式验证
- **头部验证**: 检查帧头是否为`0x55 0xAA`
- **长度验证**: 检查数据长度是否符合协议要求
- **CRC验证**: 验证数据完整性

## 8. 通知数据格式

### 8.1 自定义通知数据
设备可能主动发送以`0x02`开头的自定义通知数据：

```
02 [SEQ] 01 00 00 03 00 12 0D [CUSTOM_DATA]
```

**字段说明**:
- `0x02`: 自定义数据标识符
- `SEQ`: 递增序列号（分包传输）
- `CUSTOM_DATA`: 自定义数据内容

### 8.2 处理建议
- 非标准BLE响应格式的数据应跳过状态解析
- 可用于设备状态广播或特殊通知

## 9. 实现注意事项

### 9.1 连接管理
- 支持自动重连机制
- 处理连接状态变化
- 实现连接超时检测

### 9.2 数据传输
- 实现命令队列管理
- 支持响应超时处理
- 提供重试机制

### 9.3 状态同步
- 定期查询设备状态
- 处理状态变化通知
- 维护本地状态缓存

## 10. 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v3.0 | 2025-01-31 | 完善协议文档，添加异常处理和通知数据格式 |
| v2.0 | 2024-07-31 | 添加工作模式和状态查询功能 |
| v1.0 | 2024-01-01 | 初始版本，基本电源控制功能 |

---

**注意**: 本文档基于XH008蓝牙香薰机的实际实现代码生成，如有疑问请参考源代码实现。