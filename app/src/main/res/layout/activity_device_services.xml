<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/gray_50"
    android:fitsSystemWindows="true"
    tools:context=".fastble_demo.DeviceServicesActivity">

    <!-- 设备信息卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 设备名称 -->
            <TextView
                android:id="@+id/tv_device_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/gray_900"
                android:gravity="center"
                tools:text="XH-008 香薰机" />

            <!-- 设备MAC地址 -->
            <TextView
                android:id="@+id/tv_device_mac"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="14sp"
                android:textColor="@color/gray_600"
                android:gravity="center"
                tools:text="MAC: 00:11:22:33:44:55" />

            <!-- 连接状态指示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal"
                android:gravity="center">

                <View
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@drawable/bg_status_indicator"
                    android:backgroundTint="@color/green_500" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="已连接"
                    android:textSize="12sp"
                    android:textColor="@color/green_500"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 控制按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_switch_off"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="关闭设备"
            android:textSize="14sp"
            app:icon="@android:drawable/ic_lock_power_off"
            app:iconGravity="textStart"
            style="@style/Widget.Material3.Button" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_get_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="获取状态"
            android:textSize="14sp"
            app:icon="@android:drawable/ic_menu_info_details"
            app:iconGravity="textStart"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- 服务列表标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:text="设备服务列表"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/gray_900" />

    <!-- 服务列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_services"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:clipToPadding="false"
        android:paddingBottom="8dp"
        tools:listitem="@layout/item_ble_service" />

</LinearLayout>