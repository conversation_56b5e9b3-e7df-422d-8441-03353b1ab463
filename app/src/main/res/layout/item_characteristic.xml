<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/bg_characteristic_item"
    android:layout_marginVertical="2dp">

    <!-- 特征UUID -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="UUID:"
            android:textSize="10sp"
            android:textColor="@color/gray_600"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_characteristic_uuid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:textSize="10sp"
            android:textColor="@color/gray_800"
            android:fontFamily="monospace"
            tools:text="00002A19-0000-1000-8000-00805F9B34FB" />

    </LinearLayout>

    <!-- 特征属性标签 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        android:gravity="start">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="属性:"
            android:textSize="10sp"
            android:textColor="@color/gray_600"
            android:textStyle="bold"
            android:layout_marginEnd="4dp" />

        <!-- 可读属性 -->
        <TextView
            android:id="@+id/tv_property_read"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="READ"
            android:textSize="9sp"
            android:textColor="@color/white"
            android:background="@drawable/bg_property_chip_read"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:layout_marginEnd="2dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 可写属性 -->
        <TextView
            android:id="@+id/tv_property_write"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="WRITE"
            android:textSize="9sp"
            android:textColor="@color/white"
            android:background="@drawable/bg_property_chip_write"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:layout_marginEnd="2dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 可通知属性 -->
        <TextView
            android:id="@+id/tv_property_notify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="NOTIFY"
            android:textSize="9sp"
            android:textColor="@color/white"
            android:background="@drawable/bg_property_chip_notify"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:layout_marginEnd="2dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 可指示属性 -->
        <TextView
            android:id="@+id/tv_property_indicate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="INDICATE"
            android:textSize="9sp"
            android:textColor="@color/white"
            android:background="@drawable/bg_property_chip_indicate"
            android:paddingHorizontal="4dp"
            android:paddingVertical="1dp"
            android:layout_marginEnd="2dp"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</LinearLayout>