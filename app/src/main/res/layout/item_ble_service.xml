<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_service"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/gray_200">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 服务UUID -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="服务UUID:"
                android:textSize="12sp"
                android:textColor="@color/gray_600"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_service_uuid"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:textSize="12sp"
                android:textColor="@color/gray_900"
                android:fontFamily="monospace"
                tools:text="0000180F-0000-1000-8000-00805F9B34FB" />

        </LinearLayout>

        <!-- 服务类型 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="服务类型:"
                android:textSize="12sp"
                android:textColor="@color/gray_600"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_service_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textSize="12sp"
                android:textColor="@color/blue_600"
                android:background="@drawable/bg_service_type_chip"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                tools:text="PRIMARY" />

        </LinearLayout>

        <!-- 特征数量 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="特征数量:"
                android:textSize="12sp"
                android:textColor="@color/gray_600"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_characteristics_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textSize="12sp"
                android:textColor="@color/green_600"
                android:textStyle="bold"
                tools:text="3 个特征" />

        </LinearLayout>

        <!-- 特征详细信息列表 -->
        <LinearLayout
            android:id="@+id/ll_characteristics_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            android:background="@color/gray_50"
            android:padding="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="特征详情:"
                android:textSize="11sp"
                android:textColor="@color/gray_600"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <!-- 特征列表容器 -->
            <LinearLayout
                android:id="@+id/ll_characteristics_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

        </LinearLayout>

        <!-- 服务描述 -->
        <TextView
            android:id="@+id/tv_service_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="11sp"
            android:textColor="@color/gray_500"
            android:textStyle="italic"
            android:visibility="gone"
            tools:text="电池服务 - 提供设备电池电量信息"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>