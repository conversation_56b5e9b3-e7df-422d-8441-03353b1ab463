<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_device"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 设备名称和连接状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_device_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/gray_900"
                tools:text="XH-008 香薰机" />

            <TextView
                android:id="@+id/tv_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_chip"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginEnd="8dp"
                tools:text="已连接"
                tools:textColor="@color/green_500" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_device_action"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="操作"
                android:textSize="12sp"
                android:visibility="gone"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:cornerRadius="16dp"
                android:minWidth="60dp"
                android:paddingHorizontal="12dp"
                android:paddingVertical="0dp" />

        </LinearLayout>

        <!-- MAC地址 -->
        <TextView
            android:id="@+id/tv_mac_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:textColor="@color/gray_600"
            android:fontFamily="monospace"
            tools:text="AA:BB:CC:DD:EE:FF" />

        <!-- 信号强度 -->
        <TextView
            android:id="@+id/tv_rssi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="@color/gray_500"
            tools:text="-65 dBm (中)" />

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="@color/gray_200" />

        <!-- 操作提示 -->
        <TextView
            android:id="@+id/tv_operation_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击连接/断开设备"
            android:textSize="10sp"
            android:textColor="@color/gray_400"
            android:textAlignment="center" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>