package com.imaginedays.xhble

import android.app.Application
import com.clj.fastble.BleManager
import com.clj.fastble.BuildConfig
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class MyApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化FastBLE
        initFastBle()
    }
    
    /**
     * 初始化FastBLE库
     */
    private fun initFastBle() {
        BleManager.getInstance().init(this)
        BleManager.getInstance()
            .enableLog(BuildConfig.DEBUG)
            .setReConnectCount(2, 10000)
            .setConnectOverTime(20000)
            .setOperateTimeout(5000)
    }
}
