# FastBLE 功能演示

这是一个基于 FastBLE 开源库实现的完整蓝牙低功耗(BLE)通信解决方案。

## 功能特性

### 1. 蓝牙设备扫描
- 支持指定设备名称扫描
- 支持指定服务UUID扫描
- 实时显示设备信息（名称、MAC地址、信号强度）
- 自动去重和更新设备列表

### 2. 设备连接管理
- 一键连接/断开设备
- 连接状态实时监控
- 支持多设备同时连接
- 自动服务发现

### 3. 数据传输
- 数据读取功能
- 数据写入功能
- 通知/指示功能
- MTU大小设置

### 4. 状态管理
- 连接状态跟踪
- 错误处理和重试机制
- 回调事件通知

## 项目结构

```
fastble_demo/
├── manager/
│   └── FastBleManager.kt          # 主管理类，统一管理所有BLE功能
├── scanner/
│   └── BleDeviceScanner.kt        # 设备扫描器
├── connection/
│   └── BleConnectionManager.kt    # 连接管理器
├── transfer/
│   └── BleDataTransfer.kt         # 数据传输类
├── callback/
│   ├── BleScanCallback.kt         # 扫描回调接口
│   ├── BleConnectionCallback.kt   # 连接回调接口
│   └── BleDataCallback.kt         # 数据传输回调接口
├── model/
│   ├── BleDeviceInfo.kt           # 设备信息数据类
│   └── ConnectionState.kt         # 连接状态枚举
├── adapter/
│   └── BleDeviceAdapter.kt        # 设备列表适配器
├── FastBleDemoActivity.kt         # 演示Activity
└── README.md                      # 使用说明
```

## 使用方法

### 1. 初始化

```kotlin
val fastBleManager = FastBleManager.getInstance()
fastBleManager.init(context)
```

### 2. 扫描设备

```kotlin
// 开始扫描（10秒超时）
fastBleManager.startScan(timeoutMillis = 10000)

// 停止扫描
fastBleManager.stopScan()

// 添加扫描回调
fastBleManager.addScanCallback(object : BleScanCallback {
    override fun onScanStarted() {
        // 扫描开始
    }
    
    override fun onDeviceFound(deviceInfo: BleDeviceInfo) {
        // 发现设备
    }
    
    override fun onScanFinished(scanResults: List<BleDeviceInfo>) {
        // 扫描完成
    }
    
    override fun onScanFailed(errorCode: Int, errorMessage: String) {
        // 扫描失败
    }
})
```

### 3. 连接设备

```kotlin
// 连接设备
fastBleManager.connect(deviceInfo)

// 断开设备
fastBleManager.disconnect(deviceInfo)

// 断开所有设备
fastBleManager.disconnectAll()

// 添加连接回调
fastBleManager.addConnectionCallback(object : BleConnectionCallback {
    override fun onConnectSuccess(deviceInfo: BleDeviceInfo) {
        // 连接成功
    }
    
    override fun onConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {
        // 连接失败
    }
    
    override fun onDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {
        // 设备断开
    }
    
    override fun onServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
        // 服务发现完成
    }
})
```

### 4. 数据传输

```kotlin
// 写入数据
fastBleManager.writeData(
    deviceInfo = deviceInfo,
    serviceUuid = "your-service-uuid",
    characteristicUuid = "your-characteristic-uuid",
    data = byteArrayOf(0x01, 0x02, 0x03)
)

// 读取数据
fastBleManager.readData(
    deviceInfo = deviceInfo,
    serviceUuid = "your-service-uuid",
    characteristicUuid = "your-characteristic-uuid"
)

// 设置通知
fastBleManager.setNotification(
    deviceInfo = deviceInfo,
    serviceUuid = "your-service-uuid",
    characteristicUuid = "your-characteristic-uuid",
    enable = true
)

// 添加数据传输回调
fastBleManager.addDataCallback(object : BleDataCallback {
    override fun onWriteSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
        // 写入成功
    }
    
    override fun onReadSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
        // 读取成功
    }
    
    override fun onNotificationReceived(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
        // 收到通知数据
    }
})
```

## 权限要求

### Android 12 (API 31) 及以上
```xml
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

### Android 11 (API 30) 及以下
```xml
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

## 注意事项

1. **权限检查**: 使用前必须检查并申请相应的蓝牙权限
2. **蓝牙状态**: 确保设备蓝牙已开启
3. **线程安全**: 所有回调都在主线程中执行
4. **资源释放**: Activity销毁时记得移除回调监听器
5. **错误处理**: 建议对所有操作添加错误处理逻辑

## 依赖库

```kotlin
implementation("com.clj.fastble:FastBleLib:2.4.0")
```

## 示例应用

运行 `FastBleDemoActivity` 可以体验完整的BLE功能演示，包括：
- 设备扫描和列表显示
- 设备连接状态管理
- 实时状态更新
- 用户交互界面

这个演示应用展示了如何在实际项目中集成和使用FastBLE功能。