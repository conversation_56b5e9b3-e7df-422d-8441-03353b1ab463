package com.imaginedays.xhble.fastble_demo

import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.clj.fastble.BleManager
import com.clj.fastble.callback.BleGattCallback
import com.clj.fastble.callback.BleIndicateCallback
import com.clj.fastble.callback.BleNotifyCallback
import com.clj.fastble.callback.BleReadCallback
import com.clj.fastble.callback.BleWriteCallback
import com.clj.fastble.data.BleDevice
import com.clj.fastble.exception.BleException
import android.os.Handler
import android.os.Looper
import com.imaginedays.xhble.R
import com.imaginedays.xhble.fastble_demo.adapter.BleServiceAdapter
import com.imaginedays.xhble.fastble_demo.constants.BleUuidConstants
import java.util.UUID

class DeviceServicesActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_DEVICE_NAME = "device_name"
        const val EXTRA_DEVICE_MAC = "device_mac"
        private const val TAG = "imaginedays"
        
        // 命令常量
        private const val CMD_GET_DEVICE_STATUS = 0x0B.toByte()
        private const val CMD_SWITCH_CONTROL = 0x00.toByte()
    }

    private lateinit var tvDeviceName: TextView
    private lateinit var tvDeviceMac: TextView
    private lateinit var recyclerViewServices: RecyclerView
    private lateinit var serviceAdapter: BleServiceAdapter

    private var deviceName: String? = null
    private var deviceMac: String? = null
    private var bleDevice: BleDevice? = null
    private var connectionStateCallback: BleGattCallback? = null
    private var responseTimeoutHandler: Handler? = null
    private var responseTimeoutRunnable: Runnable? = null
    private var isWaitingForResponse = false
    private val RESPONSE_TIMEOUT_MS = 8000L // 8秒超时

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_services)

        // 设置ActionBar
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "设备服务列表"
        }

        initViews()
        initData()
        setupConnectionStateMonitoring()
        loadDeviceServices()
    }

    private fun initViews() {
        tvDeviceName = findViewById(R.id.tv_device_name)
        tvDeviceMac = findViewById(R.id.tv_device_mac)
        recyclerViewServices = findViewById(R.id.recycler_view_services)

        // 设置RecyclerView
        serviceAdapter = BleServiceAdapter(this)
        recyclerViewServices.apply {
            layoutManager = LinearLayoutManager(this@DeviceServicesActivity)
            adapter = serviceAdapter
        }
        
        // 设置服务点击监听器
        serviceAdapter.setOnServiceClickListener { service ->
            onServiceClicked(service)
        }
        
        // 设置控制按钮点击监听器
        findViewById<com.google.android.material.button.MaterialButton>(R.id.btn_switch_off).setOnClickListener {
            sendSwitchOffCommand()
        }
        
        findViewById<com.google.android.material.button.MaterialButton>(R.id.btn_get_status).setOnClickListener {
            sendGetDeviceStatusCommand()
        }
    }

    private fun initData() {
        deviceName = intent.getStringExtra(EXTRA_DEVICE_NAME)
        deviceMac = intent.getStringExtra(EXTRA_DEVICE_MAC)

        tvDeviceName.text = deviceName ?: "未知设备"
        tvDeviceMac.text = "MAC: ${deviceMac ?: "未知"}"

        // 获取BleDevice对象
        deviceMac?.let { mac ->
            bleDevice = BleManager.getInstance().allConnectedDevice.find { it.mac == mac }
        }
        
        // 初始化Handler
        responseTimeoutHandler = Handler(Looper.getMainLooper())
    }

    /**
     * 设置连接状态监听
     */
    private fun setupConnectionStateMonitoring() {
        Log.d(TAG, "[BLE_MONITOR] 设置连接状态监听器")
        
        bleDevice?.let { device ->
            // 检查设备是否已经连接
            if (BleManager.getInstance().isConnected(device)) {
                Log.d(TAG, "[BLE_MONITOR] 设备已连接，设置断开监听")
                
                connectionStateCallback = object : BleGattCallback() {
                    override fun onStartConnect() {
                        Log.d(TAG, "[BLE_MONITOR] 连接状态: 开始连接")
                    }
                    
                    override fun onConnectFail(bleDevice: BleDevice?, exception: BleException?) {
                        Log.w(TAG, "[BLE_MONITOR] 连接状态: 连接失败 - ${exception?.description}")
                        runOnUiThread {
                            handleConnectionLost("连接失败: ${exception?.description}")
                        }
                    }
                    
                    override fun onConnectSuccess(bleDevice: BleDevice?, gatt: android.bluetooth.BluetoothGatt?, status: Int) {
                        Log.d(TAG, "[BLE_MONITOR] 连接状态: 连接成功")
                    }
                    
                    override fun onDisConnected(isActiveDisConnected: Boolean, bleDevice: BleDevice?, gatt: android.bluetooth.BluetoothGatt?, status: Int) {
                        Log.w(TAG, "[BLE_MONITOR] 连接状态: 设备断开连接")
                        Log.w(TAG, "[BLE_MONITOR] 主动断开: $isActiveDisConnected, 状态码: $status")
                        
                        if (!isActiveDisConnected) {
                            runOnUiThread {
                                val statusMessage = when (status) {
                                    8 -> "连接超时，设备可能距离过远"
                                    19 -> "设备主动断开连接"
                                    133 -> "GATT连接错误"
                                    else -> "设备意外断开连接 (状态码: $status)"
                                }
                                handleConnectionLost(statusMessage)
                            }
                        }
                    }
                }
                
                // 为已连接的设备设置断开监听
                // 注意：这里不调用connect，而是通过其他方式监听连接状态变化
                Log.d(TAG, "[BLE_MONITOR] 连接状态监听器已设置")
            } else {
                Log.w(TAG, "[BLE_MONITOR] 设备未连接，无法设置监听")
            }
        }
    }
    
    /**
     * 处理连接丢失
     */
    private fun handleConnectionLost(reason: String) {
        Log.e(TAG, "[BLE_MONITOR] 处理连接丢失: $reason")
        
        // 停止等待响应
        stopWaitingForResponse()
        
        // 显示连接丢失提示
        Toast.makeText(this, "设备连接已断开: $reason", Toast.LENGTH_LONG).show()
        
        // 显示重连对话框
        bleDevice?.let { device ->
            showReconnectDialog(device)
        }
    }
    
    /**
     * 开始等待响应
     */
    private fun startWaitingForResponse() {
        Log.d(TAG, "[BLE_TIMEOUT] 开始等待设备响应，超时时间: ${RESPONSE_TIMEOUT_MS}ms")
        
        isWaitingForResponse = true
        
        responseTimeoutRunnable = Runnable {
            Log.w(TAG, "[BLE_TIMEOUT] 响应超时，停止等待")
            
            runOnUiThread {
                isWaitingForResponse = false
                
                // 检查设备是否仍然连接
                bleDevice?.let { device ->
                    if (!checkDeviceConnection(device)) {
                        Log.w(TAG, "[BLE_TIMEOUT] 超时期间设备已断开连接")
                        Toast.makeText(this@DeviceServicesActivity, "设备响应超时，连接可能已断开", Toast.LENGTH_LONG).show()
                        showReconnectDialog(device)
                    } else {
                        Log.w(TAG, "[BLE_TIMEOUT] 设备仍连接但响应超时")
                        Toast.makeText(this@DeviceServicesActivity, "设备响应超时，请重试", Toast.LENGTH_LONG).show()
                    }
                }
            }
        }
        
        responseTimeoutHandler?.postDelayed(responseTimeoutRunnable!!, RESPONSE_TIMEOUT_MS)
    }
    
    /**
     * 停止等待响应
     */
    private fun stopWaitingForResponse() {
        if (isWaitingForResponse) {
            Log.d(TAG, "[BLE_TIMEOUT] 停止等待响应")
            
            isWaitingForResponse = false
            responseTimeoutRunnable?.let { runnable ->
                responseTimeoutHandler?.removeCallbacks(runnable)
            }
        }
    }

    private fun loadDeviceServices() {
        bleDevice?.let { device ->
            if (BleManager.getInstance().isConnected(device)) {
                // 获取GATT服务列表
                val services = BleManager.getInstance().getBluetoothGatt(device)?.services
                if (services != null && services.isNotEmpty()) {
                    serviceAdapter.updateServices(services)
                    printServicesAndCharacteristics(services)
                } else {
                    // 如果服务列表为空，尝试重新发现服务
                    discoverServices(device)
                }
            } else {
                Toast.makeText(this, "设备未连接", Toast.LENGTH_SHORT).show()
                finish()
            }
        } ?: run {
            Toast.makeText(this, "设备信息错误", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun discoverServices(device: BleDevice) {
        // 直接从BluetoothGatt获取服务列表
        val gatt = BleManager.getInstance().getBluetoothGatt(device)
        if (gatt != null) {
            // 先尝试获取已缓存的服务
            var services = gatt.services
            if (services.isNullOrEmpty()) {
                // 如果没有服务，触发服务发现
                val discoverResult = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    if (checkSelfPermission(android.Manifest.permission.BLUETOOTH_CONNECT) == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                        gatt.discoverServices()
                    } else {
                        false
                    }
                } else {
                    gatt.discoverServices()
                }
                if (discoverResult) {
                    // 等待一段时间让服务发现完成
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        services = gatt.services
                        if (services != null && services.isNotEmpty()) {
                            serviceAdapter.updateServices(services)
                        } else {
                            Toast.makeText(this@DeviceServicesActivity, "未发现任何服务", Toast.LENGTH_SHORT).show()
                        }
                    }, 2000) // 等待2秒
                } else {
                    Toast.makeText(this, "启动服务发现失败", Toast.LENGTH_SHORT).show()
                }
            } else {
                // 直接使用已缓存的服务
                serviceAdapter.updateServices(services)
            }
        } else {
            Toast.makeText(this, "无法获取GATT连接", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 服务点击事件处理
     */
    private fun onServiceClicked(service: BluetoothGattService) {
        val serviceUuid = service.uuid.toString().uppercase()
        Log.d(TAG, "[BLE_CLICK] 用户点击服务: $serviceUuid")
        Log.d(TAG, "[BLE_CLICK] 服务名称: ${getServiceName(serviceUuid)}")
        
        // 对点击服务执行获取设备状态流程
        Log.d(TAG, "[BLE_PROTOCOL] 开始执行获取设备状态流程")
        sendGetDeviceStatusCommand(service)
    }
    
    /**
     * 特征值查找结果数据类
     * 
     * 封装了在GATT服务中查找到的不同类型特征值，用于后续的BLE操作
     * 
     * @param writeCharacteristic 支持写入操作的特征值，如果服务不支持写入则为null
     * @param readCharacteristic 支持读取操作的特征值，如果服务不支持读取则为null
     * @param notifyCharacteristic 支持通知/指示操作的特征值，如果服务不支持通知则为null
     */
    data class CharacteristicSearchResult(
        val writeCharacteristic: BluetoothGattCharacteristic?,
        val readCharacteristic: BluetoothGattCharacteristic?,
        val notifyCharacteristic: BluetoothGattCharacteristic?
    )

    /**
     * BLE命令执行策略枚举
     * 
     * 定义了四种不同的BLE命令执行模式，用于适应不同设备的特征值支持情况。
     * 系统会根据设备支持的特征值自动选择合适的策略，或在策略不匹配时降级到备用策略。
     */
    enum class BleCommandStrategy {
        /** 写入命令并启用通知接收响应 - 适用于支持写入和通知的设备，这是最常用的模式 */
        WRITE_WITH_NOTIFY,
        /** 仅写入命令，不接收响应 - 适用于只支持写入的设备，或作为WRITE_WITH_NOTIFY的降级策略 */
        WRITE_ONLY,
        /** 读取命令并启用通知接收响应 - 适用于支持读取和通知的设备 */
        READ_WITH_NOTIFY,
        /** 仅读取命令，不接收响应 - 适用于只支持读取的设备，或作为READ_WITH_NOTIFY的降级策略 */
        READ_ONLY
    }

    /**
     * BLE命令执行配置数据类
     * 
     * 封装了执行BLE命令所需的所有配置信息，包括命令数据、执行策略和用户反馈消息。
     * 这个类使得BLE命令的执行更加标准化和可配置。
     * 
     * @param command 要发送的命令字节数组，通常是协议规定的特定格式数据
     * @param strategy 命令执行策略，决定使用哪种操作模式（写入/读取，是否启用通知）
     * @param successMessage 操作成功时显示给用户的消息，默认为"命令执行成功"
     * @param failureMessage 操作失败时显示给用户的消息，默认为"命令执行失败"
     */
    data class BleCommandConfig(
        val command: ByteArray,
        val strategy: BleCommandStrategy,
        val successMessage: String = "命令执行成功",
        val failureMessage: String = "命令执行失败"
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as BleCommandConfig
            if (!command.contentEquals(other.command)) return false
            if (strategy != other.strategy) return false
            if (successMessage != other.successMessage) return false
            if (failureMessage != other.failureMessage) return false
            return true
        }

        override fun hashCode(): Int {
            var result = command.contentHashCode()
            result = 31 * result + strategy.hashCode()
            result = 31 * result + successMessage.hashCode()
            result = 31 * result + failureMessage.hashCode()
            return result
        }
    }

    /**
     * 根据属性查找服务中的特征值
     * 
     * 在指定的GATT服务中查找具有不同属性的特征值，包括写入、读取和通知特征值。
     * 对于通知特征值，还会验证是否存在必需的CCCD描述符。
     * 
     * @param service 要搜索的蓝牙GATT服务
     * @return CharacteristicSearchResult 包含找到的不同属性特征值的结果对象
     */
    private fun findCharacteristicsByProperties(service: BluetoothGattService): CharacteristicSearchResult {
        Log.d(TAG, "[BLE_SEARCH] 开始查找服务特征值")
        Log.d(TAG, "[BLE_SEARCH] 目标服务UUID: ${service.uuid}")
        
        // 查找具有写入属性的特征值
        val writeCharacteristic = service.characteristics?.find { characteristic ->
            val properties = characteristic.properties
            val hasWrite = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                          (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0
            Log.d(TAG, "[BLE_SEARCH] 检查特征值 ${characteristic.uuid}: 写入属性=$hasWrite")
            hasWrite
        }

        // 查找具有读取属性的特征值
        val readCharacteristic = service.characteristics?.find { characteristic ->
            val properties = characteristic.properties
            val hasRead = (properties and BluetoothGattCharacteristic.PROPERTY_READ) != 0
            Log.d(TAG, "[BLE_SEARCH] 检查特征值 ${characteristic.uuid}: 读取属性=$hasRead")
            hasRead
        }
        
        // 查找具有通知属性的特征值，并验证descriptor
        val notifyCharacteristic = service.characteristics?.find { characteristic ->
            val properties = characteristic.properties
            val hasNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                           (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
            
            // 检查是否有Client Characteristic Configuration Descriptor
            val hasDescriptor = if (hasNotify) {
                val cccdUuid = UUID.fromString(BleUuidConstants.CCCD_UUID)
                val descriptor = characteristic.getDescriptor(cccdUuid)
                val descriptorExists = descriptor != null
                Log.d(TAG, "[BLE_SEARCH] 特征值 ${characteristic.uuid}: 通知属性=$hasNotify, CCCD描述符=$descriptorExists")
                descriptorExists
            } else {
                Log.d(TAG, "[BLE_SEARCH] 特征值 ${characteristic.uuid}: 通知属性=$hasNotify")
                false
            }
            
            hasNotify && hasDescriptor
        }
        
        Log.d(TAG, "[BLE_SEARCH] 特征值查找结果:")
        Log.d(TAG, "[BLE_SEARCH] - 写入特征值: ${if (writeCharacteristic != null) "找到 (${writeCharacteristic.uuid})" else "未找到"}")
        Log.d(TAG, "[BLE_SEARCH] - 读取特征值: ${if (readCharacteristic != null) "找到 (${readCharacteristic.uuid})" else "未找到"}")
        Log.d(TAG, "[BLE_SEARCH] - 通知特征值: ${if (notifyCharacteristic != null) "找到 (${notifyCharacteristic.uuid})" else "未找到"}")
        
        return CharacteristicSearchResult(
            writeCharacteristic = writeCharacteristic,
            readCharacteristic = readCharacteristic,
            notifyCharacteristic = notifyCharacteristic
        )
    }

    /**
     * 通用BLE命令执行方法
     * 
     * 这是一个高度通用化的BLE命令执行方法，能够根据配置的策略自动选择合适的执行方式。
     * 方法会首先验证设备连接状态，然后查找服务中的特征值，最后根据策略执行相应的操作。
     * 
     * 支持的执行策略：
     * - WRITE_WITH_NOTIFY: 写入命令并启用通知接收响应
     * - WRITE_ONLY: 仅写入命令，不接收响应
     * - READ_WITH_NOTIFY: 读取命令并启用通知接收响应
     * - READ_ONLY: 仅读取命令，不接收响应
     * 
     * @param service 要操作的蓝牙GATT服务
     * @param config 命令执行配置，包含命令数据、执行策略和反馈消息
     */
    private fun executeBleCommand(service: BluetoothGattService, config: BleCommandConfig) {
        Log.d(TAG, "[BLE_EXECUTE] 开始执行BLE命令")
        Log.d(TAG, "[BLE_EXECUTE] 目标服务UUID: ${service.uuid}")
        Log.d(TAG, "[BLE_EXECUTE] 执行策略: ${config.strategy}")
        Log.d(TAG, "[BLE_EXECUTE] 命令数据: ${bytesToHex(config.command)}")
        
        bleDevice?.let { device ->
            // 首先检查设备连接状态
            if (!checkDeviceConnection(device)) {
                Log.e(TAG, "[BLE_ERROR] 设备未连接，无法执行BLE操作")
                showReconnectDialog(device)
                return
            }
            Log.d(TAG, "[BLE_EXECUTE] 设备连接状态验证通过")
            Log.d(TAG, "[BLE_EXECUTE] 设备信息 - 名称: ${device.name}, MAC: ${device.mac}")
            
            // 查找特征值
            val searchResult = findCharacteristicsByProperties(service)
            
            // 根据策略执行命令
            when (config.strategy) {
                BleCommandStrategy.WRITE_WITH_NOTIFY -> {
                    executeWriteWithNotify(device, searchResult, config)
                }
                BleCommandStrategy.WRITE_ONLY -> {
                    executeWriteOnly(device, searchResult, config)
                }
                BleCommandStrategy.READ_WITH_NOTIFY -> {
                    executeReadWithNotify(device, searchResult, config)
                }
                BleCommandStrategy.READ_ONLY -> {
                    executeReadOnly(device, searchResult, config)
                }
            }
        } ?: run {
            Log.e(TAG, "[BLE_ERROR] BLE设备对象为空")
        }
    }

    /**
     * 执行写入+通知策略
     * 
     * 这是最常用的BLE操作模式，先启用通知接收响应，然后发送写入命令。
     * 适用于需要确认命令执行结果的场景。
     * 
     * @param device BLE设备对象
     * @param searchResult 特征值查找结果
     * @param config 命令执行配置
     */
    private fun executeWriteWithNotify(device: BleDevice, searchResult: CharacteristicSearchResult, config: BleCommandConfig) {
        val writeCharacteristic = searchResult.writeCharacteristic
        val notifyCharacteristic = searchResult.notifyCharacteristic
        
        if (writeCharacteristic != null && notifyCharacteristic != null) {
            Log.d(TAG, "[BLE_EXECUTE] 执行写入+通知策略")
            enableNotification(device, notifyCharacteristic) {
                writeCommand(device, writeCharacteristic, config.command)
            }
        } else {
            Log.e(TAG, "[BLE_ERROR] 写入+通知策略所需特征值不完整")
            Toast.makeText(this, "该服务不支持写入+通知操作", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 执行仅写入策略
     * 
     * 直接发送写入命令，不接收响应。适用于不需要确认结果的场景，
     * 或作为写入+通知策略的降级方案。
     * 
     * @param device BLE设备对象
     * @param searchResult 特征值查找结果
     * @param config 命令执行配置
     */
    private fun executeWriteOnly(device: BleDevice, searchResult: CharacteristicSearchResult, config: BleCommandConfig) {
        val writeCharacteristic = searchResult.writeCharacteristic
        
        if (writeCharacteristic != null) {
            Log.d(TAG, "[BLE_EXECUTE] 执行仅写入策略")
            Toast.makeText(this, "该服务不支持通知，将尝试仅写入模式", Toast.LENGTH_SHORT).show()
            writeCommand(device, writeCharacteristic, config.command)
        } else {
            Log.e(TAG, "[BLE_ERROR] 服务缺少写入特征值")
            Toast.makeText(this, "该服务不支持写入操作", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 执行读取+通知策略
     * 
     * 先启用通知接收响应，然后发送读取命令。适用于需要主动读取设备数据
     * 并接收响应的场景。
     * 
     * @param device BLE设备对象
     * @param searchResult 特征值查找结果
     * @param config 命令执行配置
     */
    private fun executeReadWithNotify(device: BleDevice, searchResult: CharacteristicSearchResult, config: BleCommandConfig) {
        val readCharacteristic = searchResult.readCharacteristic
        val notifyCharacteristic = searchResult.notifyCharacteristic
        
        if (readCharacteristic != null && notifyCharacteristic != null) {
            Log.d(TAG, "[BLE_EXECUTE] 执行读取+通知策略")
            enableNotification(device, notifyCharacteristic) {
                readCommand(device, readCharacteristic, config.command)
            }
        } else {
            Log.e(TAG, "[BLE_ERROR] 读取+通知策略所需特征值不完整")
            Toast.makeText(this, "该服务不支持读取+通知操作", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 执行仅读取策略
     * 
     * 该方法用于执行READ_ONLY策略，直接读取特征值数据而不启用通知。
     * 适用于一次性数据读取场景，如读取设备信息、配置参数等。
     * 
     * @param device BLE设备对象
     * @param searchResult 特征值查找结果，包含读取特征值
     * @param config BLE命令配置，包含要发送的命令和消息
     */
    private fun executeReadOnly(device: BleDevice, searchResult: CharacteristicSearchResult, config: BleCommandConfig) {
        val readCharacteristic = searchResult.readCharacteristic
        
        if (readCharacteristic != null) {
            Log.d(TAG, "[BLE_EXECUTE] 执行仅读取策略")
            Toast.makeText(this, "该服务不支持通知，将尝试仅读取模式", Toast.LENGTH_SHORT).show()
            readCommand(device, readCharacteristic, config.command)
        } else {
            Log.e(TAG, "[BLE_ERROR] 服务缺少读取特征值")
            Toast.makeText(this, "该服务不支持读取操作", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 发送获取设备状态命令
     */
    private fun sendGetDeviceStatusCommand(service: BluetoothGattService) {
        Log.d(TAG, "[DEVICE_STATUS] 开始发送获取设备状态命令")
        Log.d(TAG, "[DEVICE_STATUS] 目标服务UUID: ${service.uuid}")
        
        // 构建获取设备状态命令
        val command = buildGetDeviceStatusCommand()
        
        // 创建命令配置
        val config = BleCommandConfig(
            command = command,
            strategy = BleCommandStrategy.READ_WITH_NOTIFY,
            successMessage = "设备状态查询成功",
            failureMessage = "设备状态查询失败"
        )
        
        // 使用通用方法执行命令
        executeBleCommand(service, config)
    }

    /**
     * 启用通知
     */
    private fun enableNotification(device: BleDevice, characteristic: BluetoothGattCharacteristic, onSuccess: () -> Unit) {
        Log.d(TAG, "[BLE_NOTIFY] 开始启用通知特征值")
        Log.d(TAG, "[BLE_NOTIFY] 服务UUID: ${characteristic.service.uuid}")
        Log.d(TAG, "[BLE_NOTIFY] 特征值UUID: ${characteristic.uuid}")
        
        // 在启用通知前再次检查连接状态
        if (!checkDeviceConnection(device)) {
            Log.e(TAG, "[BLE_ERROR] 启用通知前检查发现设备未连接")
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "设备连接已断开，无法启用通知", Toast.LENGTH_SHORT).show()
                showReconnectDialog(device)
            }
            return
        }
        
        Log.d(TAG, "[BLE_NOTIFY] 设备连接状态验证通过，继续启用通知")
        
        // 验证特征值是否真正支持通知
        val properties = characteristic.properties
        val hasNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0
        val hasIndicate = (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
        
        Log.d(TAG, "[BLE_NOTIFY] 特征值属性检查: NOTIFY=$hasNotify, INDICATE=$hasIndicate")
        
        if (!hasNotify && !hasIndicate) {
            Log.e(TAG, "[BLE_ERROR] 特征值不支持通知或指示")
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "该特征值不支持通知功能", Toast.LENGTH_SHORT).show()
            }
            return
        }
        
        // 检查Client Characteristic Configuration Descriptor
        val cccdUuid = UUID.fromString(BleUuidConstants.CCCD_UUID)
        val descriptor = characteristic.getDescriptor(cccdUuid)
        
        if (descriptor == null) {
            Log.e(TAG, "[BLE_ERROR] 特征值缺少CCCD描述符，无法启用通知")
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "该特征值缺少通知配置描述符", Toast.LENGTH_SHORT).show()
            }
            return
        }
        
        Log.d(TAG, "[BLE_NOTIFY] CCCD描述符验证通过，开始启用通知")
        
        // 根据特征值属性选择使用indicate或notify
        if (hasIndicate) {
            Log.d(TAG, "[BLE_NOTIFY] 使用INDICATE模式")
            BleManager.getInstance().indicate(
                device,
                characteristic.service.uuid.toString(),
                characteristic.uuid.toString(),
                object : BleIndicateCallback() {
                    override fun onIndicateSuccess() {
                        Log.d(TAG, "[BLE_INDICATE] INDICATE启用成功")
                        Log.d(TAG, "[BLE_INDICATE] 设备现在可以接收数据指示")
                        runOnUiThread {
                            Toast.makeText(this@DeviceServicesActivity, "指示已启用", Toast.LENGTH_SHORT).show()
                            onSuccess()
                        }
                    }
                    
                    override fun onIndicateFailure(exception: BleException?) {
                        Log.e(TAG, "[BLE_ERROR] 启用INDICATE失败")
                        Log.e(TAG, "[BLE_ERROR] 错误信息: ${exception?.description}")
                        Log.e(TAG, "[BLE_ERROR] 错误代码: ${exception?.code}")
                        
                        // 根据错误代码提供更具体的错误信息和处理方案
                        val (errorMessage, shouldShowReconnect) = when (exception?.code) {
                            101 -> Pair("设备不支持该指示操作", false)
                            102 -> Pair("设备连接已断开", true)
                            133 -> Pair("GATT连接错误", true)
                            8 -> Pair("连接超时，请检查设备距离", true)
                            else -> Pair("启用指示失败: ${exception?.description}", false)
                        }
                        
                        Log.e(TAG, "[BLE_ERROR] 用户友好错误信息: $errorMessage")
                        Log.e(TAG, "[BLE_ERROR] 是否需要重新连接: $shouldShowReconnect")
                        
                        runOnUiThread {
                            Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                            
                            // 对于连接相关的错误，提供重新连接选项
                            if (shouldShowReconnect) {
                                bleDevice?.let { device ->
                                    Log.d(TAG, "[BLE_ERROR] 显示重新连接对话框")
                                    showReconnectDialog(device)
                                }
                            }
                        }
                    }
                    
                    override fun onCharacteristicChanged(data: ByteArray?) {
                        data?.let {
                            Log.d(TAG, "[BLE_RECEIVE] 收到设备指示数据")
                            Log.d(TAG, "[BLE_RECEIVE] 原始数据: ${bytesToHex(it)}")
                            Log.d(TAG, "[BLE_RECEIVE] 数据长度: ${it.size} 字节")
                            
                            // 停止等待响应超时计时
                            stopWaitingForResponse()
                            
                            runOnUiThread {
                                handleReceivedData(it)
                            }
                        } ?: run {
                            Log.w(TAG, "[BLE_RECEIVE] 收到空数据指示")
                        }
                    }
                }
            )
        } else {
            Log.d(TAG, "[BLE_NOTIFY] 使用NOTIFY模式")
            BleManager.getInstance().notify(
                 device,
                 characteristic.service.uuid.toString(),
                 characteristic.uuid.toString(),
                 object : BleNotifyCallback() {
                     override fun onNotifySuccess() {
                         Log.d(TAG, "[BLE_NOTIFY] 通知启用成功")
                         Log.d(TAG, "[BLE_NOTIFY] 设备现在可以接收数据通知")
                         runOnUiThread {
                             Toast.makeText(this@DeviceServicesActivity, "通知已启用", Toast.LENGTH_SHORT).show()
                             onSuccess()
                         }
                     }
                     
                     override fun onNotifyFailure(exception: BleException?) {
                         Log.e(TAG, "[BLE_ERROR] 启用通知失败")
                         Log.e(TAG, "[BLE_ERROR] 错误信息: ${exception?.description}")
                         Log.e(TAG, "[BLE_ERROR] 错误代码: ${exception?.code}")
                         
                         // 根据错误代码提供更具体的错误信息和处理方案
                         val (errorMessage, shouldShowReconnect) = when (exception?.code) {
                             101 -> Pair("设备不支持该通知操作", false)
                             102 -> Pair("设备连接已断开", true)
                             133 -> Pair("GATT连接错误", true)
                             8 -> Pair("连接超时，请检查设备距离", true)
                             else -> Pair("启用通知失败: ${exception?.description}", false)
                         }
                         
                         Log.e(TAG, "[BLE_ERROR] 用户友好错误信息: $errorMessage")
                         Log.e(TAG, "[BLE_ERROR] 是否需要重新连接: $shouldShowReconnect")
                         
                         runOnUiThread {
                             Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                             
                             // 对于连接相关的错误，提供重新连接选项
                             if (shouldShowReconnect) {
                                 bleDevice?.let { device ->
                                     Log.d(TAG, "[BLE_ERROR] 显示重新连接对话框")
                                     showReconnectDialog(device)
                                 }
                             }
                         }
                     }
                     
                     override fun onCharacteristicChanged(data: ByteArray?) {
                         data?.let {
                             Log.d(TAG, "[BLE_RECEIVE] 收到设备通知数据")
                             Log.d(TAG, "[BLE_RECEIVE] 原始数据: ${bytesToHex(it)}")
                             Log.d(TAG, "[BLE_RECEIVE] 数据长度: ${it.size} 字节")
                             
                             // 停止等待响应超时计时
                             stopWaitingForResponse()
                             
                             runOnUiThread {
                                 handleReceivedData(it)
                             }
                         } ?: run {
                             Log.w(TAG, "[BLE_RECEIVE] 收到空数据通知")
                         }
                     }
                 }
             )
         }
    }
    
    /**
     * 写入命令
     */
    private fun writeCommand(device: BleDevice, characteristic: BluetoothGattCharacteristic, command: ByteArray) {
        Log.d(TAG, "[BLE_WRITE] 开始写入命令到设备")
        Log.d(TAG, "[BLE_WRITE] 目标设备: ${device.name} (${device.mac})")
        Log.d(TAG, "[BLE_WRITE] 服务UUID: ${characteristic.service.uuid}")
        Log.d(TAG, "[BLE_WRITE] 特征值UUID: ${characteristic.uuid}")
        Log.d(TAG, "[BLE_WRITE] 命令数据: ${bytesToHex(command)}")
        Log.d(TAG, "[BLE_WRITE] 命令长度: ${command.size} 字节")
        
        // 在写入前再次检查连接状态
        if (!checkDeviceConnection(device)) {
            Log.e(TAG, "[BLE_ERROR] 写入前检查发现设备未连接")
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "设备连接已断开，无法发送命令", Toast.LENGTH_SHORT).show()
                showReconnectDialog(device)
            }
            return
        }
        
        Log.d(TAG, "[BLE_WRITE] 设备连接状态验证通过，继续写入命令")
        
        BleManager.getInstance().write(
            device,
            characteristic.service.uuid.toString(),
            characteristic.uuid.toString(),
            command,
            object : BleWriteCallback() {
                override fun onWriteSuccess(current: Int, total: Int, justWrite: ByteArray?) {
                    Log.d(TAG, "[BLE_WRITE] 命令写入成功")
                    Log.d(TAG, "[BLE_WRITE] 写入进度: $current/$total")
                    justWrite?.let {
                        Log.d(TAG, "[BLE_WRITE] 实际写入数据: ${bytesToHex(it)}")
                    }
                    Log.d(TAG, "[BLE_WRITE] 等待设备响应...")
                    
                    // 开始等待响应超时计时
                    startWaitingForResponse()
                    
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, "获取设备状态命令已发送", Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onWriteFailure(exception: BleException?) {
                    Log.e(TAG, "[BLE_ERROR] 命令写入失败")
                    Log.e(TAG, "[BLE_ERROR] 错误信息: ${exception?.description}")
                    Log.e(TAG, "[BLE_ERROR] 错误代码: ${exception?.code}")
                    
                    // 根据错误代码提供更具体的错误信息和处理方案
                    val (errorMessage, shouldShowReconnect) = when (exception?.code) {
                        102 -> Pair("设备连接已断开，无法发送命令", true)
                        133 -> Pair("GATT连接错误，无法发送命令", true)
                        8 -> Pair("连接超时，请检查设备距离", true)
                        else -> Pair("发送命令失败: ${exception?.description}", false)
                    }
                    
                    Log.e(TAG, "[BLE_ERROR] 用户友好错误信息: $errorMessage")
                    Log.e(TAG, "[BLE_ERROR] 是否需要重新连接: $shouldShowReconnect")
                    
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                        
                        // 对于连接相关的错误，提供重新连接选项
                        if (shouldShowReconnect) {
                            bleDevice?.let { device ->
                                Log.d(TAG, "[BLE_ERROR] 显示重新连接对话框")
                                showReconnectDialog(device)
                            }
                        }
                    }
                }
            }
        )
    }

    /**
     * 读取命令
     */
    private fun readCommand(
        device: BleDevice,
        readCharacteristic: BluetoothGattCharacteristic,
        command: ByteArray
    ) {
        Log.d(TAG, "[BLE_READ] 开始读取特征值数据")
        Log.d(TAG, "[BLE_READ] 目标设备: ${device.name} (${device.mac})")
        Log.d(TAG, "[BLE_READ] 服务UUID: ${readCharacteristic.service.uuid}")
        Log.d(TAG, "[BLE_READ] 特征值UUID: ${readCharacteristic.uuid}")
        Log.d(TAG, "[BLE_READ] 读取命令: ${bytesToHex(command)}")
        Log.d(TAG, "[BLE_READ] 命令长度: ${command.size} 字节")
        
        // 在读取前检查连接状态
        if (!checkDeviceConnection(device)) {
            Log.e(TAG, "[BLE_ERROR] 读取前检查发现设备未连接")
            runOnUiThread {
                Toast.makeText(this@DeviceServicesActivity, "设备连接已断开，无法读取数据", Toast.LENGTH_SHORT).show()
                showReconnectDialog(device)
            }
            return
        }
        
        Log.d(TAG, "[BLE_READ] 设备连接状态验证通过，开始读取特征值")
        
        BleManager.getInstance().read(
            device,
            readCharacteristic.service.uuid.toString(),
            readCharacteristic.uuid.toString(),
            object : BleReadCallback() {
                override fun onReadSuccess(data: ByteArray?) {
                    Log.d(TAG, "[BLE_READ] 特征值读取成功")
                    data?.let {
                        Log.d(TAG, "[BLE_READ] 读取到的数据: ${bytesToHex(it)}")
                        Log.d(TAG, "[BLE_READ] 数据长度: ${it.size} 字节")
                        
                        runOnUiThread {
                            Toast.makeText(this@DeviceServicesActivity, "数据读取成功", Toast.LENGTH_SHORT).show()
                            // 处理读取到的数据
                            handleReceivedData(it)
                        }
                    } ?: run {
                        Log.w(TAG, "[BLE_READ] 读取成功但数据为空")
                        runOnUiThread {
                            Toast.makeText(this@DeviceServicesActivity, "读取成功但数据为空", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
                
                override fun onReadFailure(exception: BleException?) {
                    Log.e(TAG, "[BLE_ERROR] 特征值读取失败")
                    Log.e(TAG, "[BLE_ERROR] 错误信息: ${exception?.description}")
                    Log.e(TAG, "[BLE_ERROR] 错误代码: ${exception?.code}")
                    
                    // 根据错误代码提供更具体的错误信息和处理方案
                    val (errorMessage, shouldShowReconnect) = when (exception?.code) {
                        102 -> Pair("设备连接已断开，无法读取数据", true)
                        133 -> Pair("GATT连接错误，无法读取数据", true)
                        8 -> Pair("连接超时，请检查设备距离", true)
                        3 -> Pair("特征值不支持读取操作", false)
                        else -> Pair("读取数据失败: ${exception?.description}", false)
                    }
                    
                    Log.e(TAG, "[BLE_ERROR] 用户友好错误信息: $errorMessage")
                    Log.e(TAG, "[BLE_ERROR] 是否需要重新连接: $shouldShowReconnect")
                    
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                        
                        // 对于连接相关的错误，提供重新连接选项
                        if (shouldShowReconnect) {
                            bleDevice?.let { device ->
                                Log.d(TAG, "[BLE_ERROR] 显示重新连接对话框")
                                showReconnectDialog(device)
                            }
                        }
                    }
                }
            }
        )
    }
    
    /**
     * 构建获取设备状态命令
     * 命令格式: AA 55 0B 01 01 [CRC]
     */
    private fun buildGetDeviceStatusCommand(): ByteArray {
        Log.d(TAG, "[BLE_BUILD] 开始构建获取设备状态命令")
        
        val command = byteArrayOf(
            0xAA.toByte(),  // 帧头1
            0x55.toByte(),  // 帧头2
            CMD_GET_DEVICE_STATUS,  // 命令字 0x0B
            0x01.toByte(),  // 数据长度
            0x01.toByte()   // 数据内容
        )
        
        Log.d(TAG, "[BLE_BUILD] 基础命令帧: ${bytesToHex(command)}")
        Log.d(TAG, "[BLE_BUILD] 帧头: AA 55")
        Log.d(TAG, "[BLE_BUILD] 命令字: 0x${String.format("%02X", CMD_GET_DEVICE_STATUS)} (获取设备状态)")
        Log.d(TAG, "[BLE_BUILD] 数据长度: 0x01")
        Log.d(TAG, "[BLE_BUILD] 数据内容: 0x01")
        
        // 计算CRC校验
        val crc = calculateCRC(command)
        Log.d(TAG, "[BLE_BUILD] CRC校验值: ${bytesToHex(crc)}")
        
        // 组合完整命令
        val fullCommand = command + crc
        Log.d(TAG, "[BLE_BUILD] 完整命令帧: ${bytesToHex(fullCommand)}")
        Log.d(TAG, "[BLE_BUILD] 命令构建完成，总长度: ${fullCommand.size} 字节")
        
        return fullCommand
    }
    
    /**
     * 构建开关关闭命令
     */
    private fun buildSwitchOffCommand(): ByteArray {
        Log.d(TAG, "[BLE_BUILD] 开始构建开关关闭命令")
        
        val command = byteArrayOf(
            0xAA.toByte(),  // 帧头1
            0x55.toByte(),  // 帧头2
            CMD_SWITCH_CONTROL,  // 命令字 0x00
            0x01.toByte(),  // 数据长度
            0x02.toByte()   // 数据内容（关闭）
        )
        
        Log.d(TAG, "[BLE_BUILD] 基础命令帧: ${bytesToHex(command)}")
        Log.d(TAG, "[BLE_BUILD] 帧头: AA 55")
        Log.d(TAG, "[BLE_BUILD] 命令字: 0x${String.format("%02X", CMD_SWITCH_CONTROL)} (开关控制)")
        Log.d(TAG, "[BLE_BUILD] 数据长度: 0x01")
        Log.d(TAG, "[BLE_BUILD] 数据内容: 0x02 (关闭)")
        
        // 计算CRC校验
        val crc = calculateCRC(command)
        Log.d(TAG, "[BLE_BUILD] CRC校验值: ${bytesToHex(crc)}")
        
        // 组合完整命令
        val fullCommand = command + crc
        Log.d(TAG, "[BLE_BUILD] 完整命令帧: ${bytesToHex(fullCommand)}")
        Log.d(TAG, "[BLE_BUILD] 开关关闭命令构建完成，总长度: ${fullCommand.size} 字节")
        
        return fullCommand
    }
    
    /**
     * 计算CRC校验（补码校验）
     */
    private fun calculateCRC(data: ByteArray): ByteArray {
        Log.d(TAG, "[BLE_CRC] 开始计算CRC校验")
        Log.d(TAG, "[BLE_CRC] 输入数据: ${bytesToHex(data)}")
        
        var sum = 0
        for ((index, byte) in data.withIndex()) {
            val byteValue = byte.toInt() and 0xFF
            sum += byteValue
            Log.d(TAG, "[BLE_CRC] 字节[$index]: 0x${String.format("%02X", byteValue)}, 累加和: 0x${String.format("%02X", sum)}")
        }
        
        Log.d(TAG, "[BLE_CRC] 最终累加和: 0x${String.format("%02X", sum)}")
        Log.d(TAG, "[BLE_CRC] 累加和低8位: 0x${String.format("%02X", sum and 0xFF)}")
        
        // 计算补码
        val crc = (0x100 - (sum and 0xFF)) and 0xFF
        Log.d(TAG, "[BLE_CRC] CRC补码计算: 0x100 - 0x${String.format("%02X", sum and 0xFF)} = 0x${String.format("%02X", crc)}")
        
        val result = byteArrayOf(crc.toByte())
        Log.d(TAG, "[BLE_CRC] CRC校验完成: ${bytesToHex(result)}")
        
        return result
    }
    
    /**
     * 处理接收到的数据
     */
    private fun handleReceivedData(data: ByteArray) {
        val hexString = bytesToHex(data)
        Log.d(TAG, "[BLE_PARSE] 开始解析接收到的数据")
        Log.d(TAG, "[BLE_PARSE] 原始响应: $hexString")
        Log.d(TAG, "[BLE_PARSE] 数据长度: ${data.size} 字节")
        
        // 验证数据帧格式
        if (data.size >= 5 && data[0] == 0xAA.toByte() && data[1] == 0x55.toByte()) {
            Log.d(TAG, "[BLE_PARSE] 帧头验证通过: AA 55")
            
            val command = data[2]
            val dataLength = data[3].toInt() and 0xFF
            
            Log.d(TAG, "[BLE_PARSE] 命令字: 0x${String.format("%02X", command)}")
            Log.d(TAG, "[BLE_PARSE] 数据长度: $dataLength")
            
            // 验证数据完整性
            if (data.size >= 5 + dataLength) {
                Log.d(TAG, "[BLE_PARSE] 数据完整性验证通过")
                
                // 验证CRC校验
                if (data.size >= 6 + dataLength) {
                    val receivedData = data.sliceArray(0 until data.size - 1)
                    val receivedCrc = data[data.size - 1]
                    val calculatedCrc = calculateCRC(receivedData)[0]
                    
                    Log.d(TAG, "[BLE_PARSE] CRC校验 - 接收: 0x${String.format("%02X", receivedCrc)}, 计算: 0x${String.format("%02X", calculatedCrc)}")
                    
                    if (receivedCrc == calculatedCrc) {
                        Log.d(TAG, "[BLE_PARSE] CRC校验通过")
                    } else {
                        Log.w(TAG, "[BLE_PARSE] CRC校验失败，但继续解析")
                    }
                }
                
                when (command) {
                    CMD_GET_DEVICE_STATUS -> {
                        Log.d(TAG, "[BLE_PARSE] 解析设备状态响应")
                        
                        val deviceData = data.sliceArray(4 until 4 + dataLength)
                        Log.d(TAG, "[BLE_PARSE] 状态数据: ${bytesToHex(deviceData)}")
                        
                        val status = if (deviceData.isNotEmpty()) deviceData[0].toInt() and 0xFF else 0
                        Log.d(TAG, "[BLE_PARSE] 状态值: 0x${String.format("%02X", status)}")
                        
                        val statusText = when (status) {
                            0x00 -> {
                                Log.d(TAG, "[BLE_PARSE] 设备状态: 关闭")
                                "设备关闭"
                            }
                            0x01 -> {
                                Log.d(TAG, "[BLE_PARSE] 设备状态: 开启")
                                "设备开启"
                            }
                            else -> {
                                Log.d(TAG, "[BLE_PARSE] 设备状态: 未知($status)")
                                "未知状态($status)"
                            }
                        }
                        
                        Log.d(TAG, "[BLE_PARSE] 状态解析完成: $statusText")
                        Toast.makeText(this, "设备状态: $statusText", Toast.LENGTH_LONG).show()
                    }
                    CMD_SWITCH_CONTROL -> {
                        Log.d(TAG, "[BLE_PARSE] 解析开关控制响应")
                        
                        val deviceData = data.sliceArray(4 until 4 + dataLength)
                        Log.d(TAG, "[BLE_PARSE] 控制响应数据: ${bytesToHex(deviceData)}")
                        
                        val result = if (deviceData.isNotEmpty()) deviceData[0].toInt() and 0xFF else 0
                        Log.d(TAG, "[BLE_PARSE] 控制结果值: 0x${String.format("%02X", result)}")
                        
                        val resultText = when (result) {
                            0x00 -> {
                                Log.d(TAG, "[BLE_PARSE] 开关控制: 成功关闭")
                                "设备已成功关闭"
                            }
                            0x01 -> {
                                Log.d(TAG, "[BLE_PARSE] 开关控制: 成功开启")
                                "设备已成功开启"
                            }
                            0xFF -> {
                                Log.d(TAG, "[BLE_PARSE] 开关控制: 操作失败")
                                "开关控制失败"
                            }
                            else -> {
                                Log.d(TAG, "[BLE_PARSE] 开关控制: 未知结果($result)")
                                "开关控制结果未知($result)"
                            }
                        }
                        
                        Log.d(TAG, "[BLE_PARSE] 开关控制解析完成: $resultText")
                        Toast.makeText(this, "开关控制: $resultText", Toast.LENGTH_LONG).show()
                    }
                    else -> {
                        Log.d(TAG, "[BLE_PARSE] 未知命令响应: 0x${String.format("%02X", command)}")
                        Toast.makeText(this, "收到响应: $hexString", Toast.LENGTH_SHORT).show()
                    }
                }
            } else {
                Log.w(TAG, "[BLE_PARSE] 数据不完整，期望长度: ${5 + dataLength}，实际长度: ${data.size}")
                Toast.makeText(this, "收到不完整数据: $hexString", Toast.LENGTH_SHORT).show()
            }
        } else {
            Log.w(TAG, "[BLE_PARSE] 帧头验证失败或数据长度不足")
            if (data.size >= 2) {
                Log.w(TAG, "[BLE_PARSE] 实际帧头: ${String.format("%02X %02X", data[0], data[1])}")
            }
            Toast.makeText(this, "收到无效数据: $hexString", Toast.LENGTH_SHORT).show()
        }
        
        Log.d(TAG, "[BLE_PARSE] 数据解析完成")
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private fun bytesToHex(bytes: ByteArray): String {
        return bytes.joinToString(" ") { "%02X".format(it) }
    }
    
    /**
     * 获取服务名称
     */
    private fun getServiceName(uuid: String): String {
        return when (uuid.uppercase()) {
            BleUuidConstants.XH008_SERVICE_UUID.uppercase() -> "XH008香薰机服务"
            BleUuidConstants.BATTERY_SERVICE_UUID.uppercase() -> "电池服务"
            BleUuidConstants.DEVICE_INFORMATION_SERVICE_UUID.uppercase() -> "设备信息服务"
            BleUuidConstants.GENERIC_ACCESS_SERVICE_UUID.uppercase() -> "通用访问服务"
            BleUuidConstants.GENERIC_ATTRIBUTE_SERVICE_UUID.uppercase() -> "通用属性服务"
            else -> "未知服务"
        }
    }
    
    /**
     * 检查设备连接状态
     */
    private fun checkDeviceConnection(device: BleDevice): Boolean {
        Log.d(TAG, "[BLE_CONNECTION] 开始检查设备连接状态")
        Log.d(TAG, "[BLE_CONNECTION] 设备信息: ${device.name} (${device.mac})")
        
        // 使用BleManager检查连接状态
        val isConnected = BleManager.getInstance().isConnected(device)
        Log.d(TAG, "[BLE_CONNECTION] BleManager连接状态: $isConnected")
        
        // 检查BluetoothGatt是否可用
        val gatt = BleManager.getInstance().getBluetoothGatt(device)
        val hasGatt = gatt != null
        Log.d(TAG, "[BLE_CONNECTION] BluetoothGatt可用性: $hasGatt")
        
        // 如果有GATT连接，检查服务是否可用
        if (hasGatt && gatt != null) {
            val services = gatt.services
            val hasServices = services != null && services.isNotEmpty()
            Log.d(TAG, "[BLE_CONNECTION] GATT服务可用性: $hasServices (服务数量: ${services?.size ?: 0})")
            
            val connectionValid = isConnected && hasServices
            Log.d(TAG, "[BLE_CONNECTION] 最终连接状态: $connectionValid")
            return connectionValid
        } else {
            Log.w(TAG, "[BLE_CONNECTION] GATT连接不可用")
            return false
        }
    }
    
    /**
     * 显示重新连接对话框
     */
    private fun showReconnectDialog(device: BleDevice) {
        Log.d(TAG, "[BLE_RECONNECT] 显示重新连接对话框")
        
        AlertDialog.Builder(this)
            .setTitle("设备连接已断开")
            .setMessage("设备 ${device.name ?: "未知设备"} 已断开连接。\n\n是否尝试重新连接？")
            .setPositiveButton("重新连接") { _, _ ->
                Log.d(TAG, "[BLE_RECONNECT] 用户选择重新连接")
                attemptReconnection(device)
            }
            .setNegativeButton("取消") { dialog, _ ->
                Log.d(TAG, "[BLE_RECONNECT] 用户取消重新连接")
                dialog.dismiss()
                Toast.makeText(this, "操作已取消", Toast.LENGTH_SHORT).show()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 尝试重新连接设备
     */
    private fun attemptReconnection(device: BleDevice) {
        Log.d(TAG, "[BLE_RECONNECT] 开始尝试重新连接设备")
        Log.d(TAG, "[BLE_RECONNECT] 目标设备: ${device.name} (${device.mac})")
        
        Toast.makeText(this, "正在重新连接设备...", Toast.LENGTH_SHORT).show()
        
        BleManager.getInstance().connect(device, object : BleGattCallback() {
            override fun onStartConnect() {
                Log.d(TAG, "[BLE_RECONNECT] 开始连接")
                runOnUiThread {
                    Toast.makeText(this@DeviceServicesActivity, "开始连接...", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onConnectFail(bleDevice: BleDevice?, exception: BleException?) {
                Log.e(TAG, "[BLE_RECONNECT] 重新连接失败")
                Log.e(TAG, "[BLE_RECONNECT] 错误信息: ${exception?.description}")
                Log.e(TAG, "[BLE_RECONNECT] 错误代码: ${exception?.code}")
                
                runOnUiThread {
                    val errorMessage = when (exception?.code) {
                        133 -> "连接失败，请确保设备在附近并重试"
                        8 -> "连接超时，请检查设备距离"
                        else -> "连接失败: ${exception?.description}"
                    }
                    Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                }
            }
            
            override fun onConnectSuccess(bleDevice: BleDevice?, gatt: android.bluetooth.BluetoothGatt?, status: Int) {
                Log.d(TAG, "[BLE_RECONNECT] 重新连接成功")
                Log.d(TAG, "[BLE_RECONNECT] 连接状态: $status")
                
                runOnUiThread {
                    Toast.makeText(this@DeviceServicesActivity, "设备重新连接成功！", Toast.LENGTH_SHORT).show()
                    
                    // 重新加载设备服务
                    loadDeviceServices()
                }
            }
            
            override fun onDisConnected(isActiveDisConnected: Boolean, bleDevice: BleDevice?, gatt: android.bluetooth.BluetoothGatt?, status: Int) {
                Log.w(TAG, "[BLE_RECONNECT] 连接过程中设备断开")
                Log.w(TAG, "[BLE_RECONNECT] 主动断开: $isActiveDisConnected, 状态: $status")
                
                if (!isActiveDisConnected) {
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, "设备连接中断", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        })
    }

    /**
     * 打印服务和特征值信息
     */
    private fun printServicesAndCharacteristics(services: List<BluetoothGattService>) {
        Log.d(TAG, "[BLE_DISCOVERY] ========== 开始打印设备服务和特征值信息 ==========")
        Log.d(TAG, "[BLE_DISCOVERY] 发现服务总数: ${services.size}")
        
        services.forEachIndexed { serviceIndex, service ->
            val serviceUuid = service.uuid.toString().uppercase()
            val serviceName = getServiceName(serviceUuid)
            
            Log.d(TAG, "[BLE_DISCOVERY] ")
            Log.d(TAG, "[BLE_DISCOVERY] 服务 ${serviceIndex + 1}/${services.size}:")
            Log.d(TAG, "[BLE_DISCOVERY] - UUID: $serviceUuid")
            Log.d(TAG, "[BLE_DISCOVERY] - 名称: $serviceName")
            Log.d(TAG, "[BLE_DISCOVERY] - 类型: ${if (service.type == BluetoothGattService.SERVICE_TYPE_PRIMARY) "主服务" else "辅助服务"}")
            
            val characteristics = service.characteristics
            if (characteristics.isNullOrEmpty()) {
                Log.d(TAG, "[BLE_DISCOVERY] - 特征值: 无")
            } else {
                Log.d(TAG, "[BLE_DISCOVERY] - 特征值数量: ${characteristics.size}")
                
                characteristics.forEachIndexed { charIndex, characteristic ->
                    val charUuid = characteristic.uuid.toString().uppercase()
                    val properties = characteristic.properties
                    
                    Log.d(TAG, "[BLE_DISCOVERY]   特征值 ${charIndex + 1}/${characteristics.size}:")
                    Log.d(TAG, "[BLE_DISCOVERY]   - UUID: $charUuid")
                    
                    // 解析特征值属性
                    val propertyList = mutableListOf<String>()
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_READ) != 0) {
                        propertyList.add("READ")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0) {
                        propertyList.add("WRITE")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                        propertyList.add("WRITE_NO_RESPONSE")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                        propertyList.add("NOTIFY")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                        propertyList.add("INDICATE")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_SIGNED_WRITE) != 0) {
                        propertyList.add("SIGNED_WRITE")
                    }
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_EXTENDED_PROPS) != 0) {
                        propertyList.add("EXTENDED_PROPS")
                    }
                    
                    Log.d(TAG, "[BLE_DISCOVERY]   - 属性: ${propertyList.joinToString(", ")}")
                    Log.d(TAG, "[BLE_DISCOVERY]   - 权限: ${getPermissionsString(characteristic.permissions)}")
                    
                    // 打印描述符信息
                    val descriptors = characteristic.descriptors
                    if (descriptors.isNullOrEmpty()) {
                        Log.d(TAG, "[BLE_DISCOVERY]   - 描述符: 无")
                    } else {
                        Log.d(TAG, "[BLE_DISCOVERY]   - 描述符数量: ${descriptors.size}")
                        descriptors.forEachIndexed { descIndex, descriptor ->
                            val descUuid = descriptor.uuid.toString().uppercase()
                            val descName = when (descUuid) {
                                BleUuidConstants.CCCD_UUID.uppercase() -> "Client Characteristic Configuration Descriptor (CCCD)"
                                "00002901-0000-1000-8000-00805F9B34FB" -> "Characteristic User Description"
                                "00002902-0000-1000-8000-00805F9B34FB" -> "Client Characteristic Configuration"
                                "00002903-0000-1000-8000-00805F9B34FB" -> "Server Characteristic Configuration"
                                "00002904-0000-1000-8000-00805F9B34FB" -> "Characteristic Presentation Format"
                                "00002905-0000-1000-8000-00805F9B34FB" -> "Characteristic Aggregate Format"
                                else -> "未知描述符"
                            }
                            Log.d(TAG, "[BLE_DISCOVERY]     描述符 ${descIndex + 1}: $descUuid ($descName)")
                        }
                    }
                }
            }
        }
        
        Log.d(TAG, "[BLE_DISCOVERY] ========== 服务和特征值信息打印完成 ==========")
    }
    
    /**
     * 获取权限字符串
     */
    private fun getPermissionsString(permissions: Int): String {
        val permissionList = mutableListOf<String>()
        
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_READ) != 0) {
            permissionList.add("READ")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_WRITE) != 0) {
            permissionList.add("WRITE")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_READ_ENCRYPTED) != 0) {
            permissionList.add("READ_ENCRYPTED")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_WRITE_ENCRYPTED) != 0) {
            permissionList.add("WRITE_ENCRYPTED")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_READ_ENCRYPTED_MITM) != 0) {
            permissionList.add("READ_ENCRYPTED_MITM")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_WRITE_ENCRYPTED_MITM) != 0) {
            permissionList.add("WRITE_ENCRYPTED_MITM")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_WRITE_SIGNED) != 0) {
            permissionList.add("WRITE_SIGNED")
        }
        if ((permissions and BluetoothGattCharacteristic.PERMISSION_WRITE_SIGNED_MITM) != 0) {
            permissionList.add("WRITE_SIGNED_MITM")
        }
        
        return if (permissionList.isEmpty()) "无" else permissionList.joinToString(", ")
    }
    
    /**
     * 发送开关关闭命令
     */
    private fun sendSwitchOffCommand() {
        Log.d(TAG, "[BLE_COMMAND] 准备发送开关关闭命令")
        
        bleDevice?.let { device ->
            // 首先检查设备连接状态
            if (!checkDeviceConnection(device)) {
                Log.e(TAG, "[BLE_ERROR] 设备未连接，无法执行开关控制")
                showReconnectDialog(device)
                return
            }
            
            // 查找XH008服务
            val gatt = BleManager.getInstance().getBluetoothGatt(device)
            val xh008Service = gatt?.services?.find { service ->
                service.uuid.toString().uppercase() == BleUuidConstants.XH008_SERVICE_UUID.uppercase()
            }
            
            if (xh008Service != null) {
                Log.d(TAG, "[BLE_COMMAND] 找到XH008服务，发送开关关闭命令")
                sendSwitchOffCommand(xh008Service)
            } else {
                Log.e(TAG, "[BLE_ERROR] 未找到XH008服务")
                Toast.makeText(this, "未找到XH008服务，无法执行开关控制", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Log.e(TAG, "[BLE_ERROR] BLE设备对象为空")
            Toast.makeText(this, "设备信息错误", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 发送开关关闭命令到指定服务
     */
    private fun sendSwitchOffCommand(service: BluetoothGattService) {
        Log.d(TAG, "[BLE_COMMAND] 准备发送开关关闭命令到服务")
        Log.d(TAG, "[BLE_COMMAND] 目标服务UUID: ${service.uuid}")
        
        bleDevice?.let { device ->
            // 查找具有写入属性的特征值
            val writeCharacteristic = service.characteristics?.find { characteristic ->
                val properties = characteristic.properties
                val hasWrite = (properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                              (properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0
                Log.d(TAG, "[BLE_COMMAND] 检查特征值 ${characteristic.uuid}: 写入属性=$hasWrite")
                hasWrite
            }
            
            // 查找具有通知属性的特征值
            val notifyCharacteristic = service.characteristics?.find { characteristic ->
                val properties = characteristic.properties
                val hasNotify = (properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                               (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0
                
                val hasDescriptor = if (hasNotify) {
                    val cccdUuid = UUID.fromString(BleUuidConstants.CCCD_UUID)
                    val descriptor = characteristic.getDescriptor(cccdUuid)
                    descriptor != null
                } else {
                    false
                }
                
                hasNotify && hasDescriptor
            }
            
            Log.d(TAG, "[BLE_COMMAND] 特征值查找结果:")
            Log.d(TAG, "[BLE_COMMAND] - 写入特征值: ${if (writeCharacteristic != null) "找到 (${writeCharacteristic.uuid})" else "未找到"}")
            Log.d(TAG, "[BLE_COMMAND] - 通知特征值: ${if (notifyCharacteristic != null) "找到 (${notifyCharacteristic.uuid})" else "未找到"}")
            
            if (writeCharacteristic != null) {
                if (notifyCharacteristic != null) {
                    Log.d(TAG, "[BLE_COMMAND] 特征值验证通过，开始启用通知")
                    // 先启用通知
                    enableNotification(device, notifyCharacteristic) {
                        // 通知启用成功后发送开关关闭命令
                        val command = buildSwitchOffCommand()
                        writeSwitchCommand(device, writeCharacteristic, command)
                    }
                } else {
                    Log.w(TAG, "[BLE_COMMAND] 未找到支持通知的特征值，尝试仅写入模式")
                    Toast.makeText(this, "该服务不支持通知，将尝试仅写入模式", Toast.LENGTH_SHORT).show()
                    // 直接发送命令，不启用通知
                    val command = buildSwitchOffCommand()
                    writeSwitchCommand(device, writeCharacteristic, command)
                }
            } else {
                Log.e(TAG, "[BLE_ERROR] 服务缺少写入特征值")
                Toast.makeText(this, "该服务不支持写入操作", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Log.e(TAG, "[BLE_ERROR] BLE设备对象为空")
        }
    }
    
    /**
     * 写入开关控制命令
     */
    private fun writeSwitchCommand(device: BleDevice, characteristic: BluetoothGattCharacteristic, command: ByteArray) {
        Log.d(TAG, "[BLE_WRITE] 开始写入开关控制命令到设备")
        Log.d(TAG, "[BLE_WRITE] 目标设备: ${device.name} (${device.mac})")
        Log.d(TAG, "[BLE_WRITE] 服务UUID: ${characteristic.service.uuid}")
        Log.d(TAG, "[BLE_WRITE] 特征值UUID: ${characteristic.uuid}")
        Log.d(TAG, "[BLE_WRITE] 命令数据: ${bytesToHex(command)}")
        Log.d(TAG, "[BLE_WRITE] 命令长度: ${command.size} 字节")
        
        BleManager.getInstance().write(
            device,
            characteristic.service.uuid.toString(),
            characteristic.uuid.toString(),
            command,
            object : BleWriteCallback() {
                override fun onWriteSuccess(current: Int, total: Int, justWrite: ByteArray?) {
                    Log.d(TAG, "[BLE_WRITE] 开关控制命令写入成功")
                    Log.d(TAG, "[BLE_WRITE] 写入进度: $current/$total")
                    justWrite?.let {
                        Log.d(TAG, "[BLE_WRITE] 实际写入数据: ${bytesToHex(it)}")
                    }
                    Log.d(TAG, "[BLE_WRITE] 等待设备响应...")
                    
                    // 开始等待响应超时计时
                    startWaitingForResponse()
                    
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, "开关关闭命令已发送", Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onWriteFailure(exception: BleException?) {
                    Log.e(TAG, "[BLE_ERROR] 开关控制命令写入失败")
                    Log.e(TAG, "[BLE_ERROR] 错误信息: ${exception?.description}")
                    Log.e(TAG, "[BLE_ERROR] 错误代码: ${exception?.code}")
                    
                    val (errorMessage, shouldShowReconnect) = when (exception?.code) {
                        102 -> Pair("设备连接已断开，无法发送开关控制命令", true)
                        133 -> Pair("GATT连接错误，无法发送开关控制命令", true)
                        8 -> Pair("连接超时，请检查设备距离", true)
                        else -> Pair("发送开关控制命令失败: ${exception?.description}", false)
                    }
                    
                    runOnUiThread {
                        Toast.makeText(this@DeviceServicesActivity, errorMessage, Toast.LENGTH_LONG).show()
                        
                        if (shouldShowReconnect) {
                            bleDevice?.let { device ->
                                showReconnectDialog(device)
                            }
                        }
                    }
                }
            }
        )
    }
    
    /**
     * 发送获取设备状态命令（无参数版本）
     */
    private fun sendGetDeviceStatusCommand() {
        Log.d(TAG, "[BLE_COMMAND] 准备发送获取设备状态命令")
        
        bleDevice?.let { device ->
            // 首先检查设备连接状态
            if (!checkDeviceConnection(device)) {
                Log.e(TAG, "[BLE_ERROR] 设备未连接，无法获取设备状态")
                showReconnectDialog(device)
                return
            }
            
            // 查找XH008服务
            val gatt = BleManager.getInstance().getBluetoothGatt(device)
            val xh008Service = gatt?.services?.find { service ->
                service.uuid.toString().uppercase() == BleUuidConstants.XH008_SERVICE_UUID.uppercase()
            }
            
            if (xh008Service != null) {
                Log.d(TAG, "[BLE_COMMAND] 找到XH008服务，发送获取设备状态命令")
                sendGetDeviceStatusCommand(xh008Service)
            } else {
                Log.e(TAG, "[BLE_ERROR] 未找到XH008服务")
                Toast.makeText(this, "未找到XH008服务，无法获取设备状态", Toast.LENGTH_SHORT).show()
            }
        } ?: run {
            Log.e(TAG, "[BLE_ERROR] BLE设备对象为空")
            Toast.makeText(this, "设备信息错误", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "[BLE_LIFECYCLE] Activity销毁，清理资源")
        
        // 停止等待响应
        stopWaitingForResponse()
        
        // 清理连接状态监听
        connectionStateCallback = null
        
        // 清理Handler
        responseTimeoutHandler?.removeCallbacksAndMessages(null)
        responseTimeoutHandler = null
        
        super.onDestroy()
    }
}
