package com.imaginedays.xhble.fastble_demo.callback

import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo

/**
 * BLE设备扫描回调接口
 */
interface BleScanCallback {
    
    /**
     * 扫描开始
     */
    fun onScanStarted()
    
    /**
     * 发现新设备
     * @param deviceInfo 设备信息
     */
    fun onDeviceFound(deviceInfo: BleDeviceInfo)
    
    /**
     * 扫描结束
     * @param scanResults 扫描到的所有设备列表
     */
    fun onScanFinished(scanResults: List<BleDeviceInfo>)
    
    /**
     * 扫描失败
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onScanFailed(errorCode: Int, errorMessage: String)
}