package com.imaginedays.xhble.fastble_demo.constants

/**
 * 蓝牙UUID常量类
 * 统一管理所有BLE相关的UUID，避免硬编码重复
 */
object BleUuidConstants {
    
    // ========== 蓝牙GATT标准UUID ==========
    
    /**
     * Client Characteristic Configuration Descriptor (CCCD) UUID
     * 用于启用/禁用特征值的通知(Notification)或指示(Indication)功能
     */
    const val CCCD_UUID = "00002902-0000-1000-8000-00805f9b34fb"
    
    // ========== XH008协议相关UUID ==========
    
    /**
     * XH008设备主服务UUID
     */
    const val XH008_SERVICE_UUID = "015f1e610-a277-43fc-a484-dd39ef8a9100"
    
    /**
     * XH008写入特征值UUID
     */
    const val XH008_WRITE_CHAR_UUID = "015f1e611-a277-43fc-a484-dd39ef8a9100"
    
    /**
     * XH008通知特征值UUID
     */
    const val XH008_NOTIFY_CHAR_UUID = "015f1e611-a277-43fc-a484-dd39ef8a9100"
    
    /**
     * XH008辅助服务UUID
     */
    const val XH008_AUXILIARY_SERVICE_UUID = "015f1e600-a277-43fc-a484-dd39ef8a9100"
    
    // ========== 蓝牙标准服务UUID ==========
    
    /**
     * 电池服务 - 提供设备电池电量信息
     */
    const val BATTERY_SERVICE_UUID = "0000180f-0000-1000-8000-00805f9b34fb"
    
    /**
     * 设备信息服务 - 提供设备制造商、型号等信息
     */
    const val DEVICE_INFORMATION_SERVICE_UUID = "0000180a-0000-1000-8000-00805f9b34fb"
    
    /**
     * 通用访问服务 - 提供设备名称、外观等基本信息
     */
    const val GENERIC_ACCESS_SERVICE_UUID = "00001800-0000-1000-8000-00805f9b34fb"
    
    /**
     * 通用属性服务 - 提供GATT服务变更通知
     */
    const val GENERIC_ATTRIBUTE_SERVICE_UUID = "00001801-0000-1000-8000-00805f9b34fb"
    
    /**
     * 自定义服务UUID - 可能用于特定功能
     */
    const val CUSTOM_SERVICE_2600_UUID = "00002600-0000-1000-8000-00805f9b34fb"
    
    /**
     * 环境感知服务 - 提供温度、湿度等环境数据
     */
    const val ENVIRONMENTAL_SENSING_SERVICE_UUID = "0000181a-0000-1000-8000-00805f9b34fb"
    
    /**
     * 人机接口设备服务 - 提供键盘、鼠标等HID功能
     */
    const val HID_SERVICE_UUID = "00001812-0000-1000-8000-00805f9b34fb"
    
    /**
     * 音频源服务 - 提供音频播放功能
     */
    const val AUDIO_SOURCE_SERVICE_UUID = "0000110a-0000-1000-8000-00805f9b34fb"
    
    /**
     * 音频接收服务 - 提供音频接收功能
     */
    const val AUDIO_SINK_SERVICE_UUID = "0000110b-0000-1000-8000-00805f9b34fb"
    
    /**
     * 对象推送服务 - 提供文件传输功能
     */
    const val OBJECT_PUSH_SERVICE_UUID = "00001105-0000-1000-8000-00805f9b34fb"
    
    /**
     * 文件传输服务 - 提供文件传输协议
     */
    const val FILE_TRANSFER_SERVICE_UUID = "00001106-0000-1000-8000-00805f9b34fb"
    
    // ========== 服务描述映射 ==========
    
    /**
     * 获取服务描述信息
     * @param uuid 服务UUID字符串
     * @return 服务描述，如果未知则返回空字符串
     */
    fun getServiceDescription(uuid: String): String {
        return when (uuid.uppercase()) {
            XH008_SERVICE_UUID.uppercase() -> "自定义服务 - 设备控制和状态通信"
            XH008_AUXILIARY_SERVICE_UUID.uppercase() -> "XH008辅助服务 - 扩展功能支持"
            CUSTOM_SERVICE_2600_UUID.uppercase() -> "自定义服务 - 特定功能实现"
            BATTERY_SERVICE_UUID.uppercase() -> "电池服务 - 提供设备电池电量信息"
            DEVICE_INFORMATION_SERVICE_UUID.uppercase() -> "设备信息服务 - 提供设备制造商、型号等信息"
            GENERIC_ACCESS_SERVICE_UUID.uppercase() -> "通用访问服务 - 提供设备名称、外观等基本信息"
            GENERIC_ATTRIBUTE_SERVICE_UUID.uppercase() -> "通用属性服务 - 提供GATT服务变更通知"
            ENVIRONMENTAL_SENSING_SERVICE_UUID.uppercase() -> "环境感知服务 - 提供温度、湿度等环境数据"
            HID_SERVICE_UUID.uppercase() -> "人机接口设备服务 - 提供键盘、鼠标等HID功能"
            AUDIO_SOURCE_SERVICE_UUID.uppercase() -> "音频源服务 - 提供音频播放功能"
            AUDIO_SINK_SERVICE_UUID.uppercase() -> "音频接收服务 - 提供音频接收功能"
            OBJECT_PUSH_SERVICE_UUID.uppercase() -> "对象推送服务 - 提供文件传输功能"
            FILE_TRANSFER_SERVICE_UUID.uppercase() -> "文件传输服务 - 提供文件传输协议"
            else -> {
                // 检查是否为自定义服务（通常以特定前缀开头）
                when {
                    uuid.startsWith("0000FFF", ignoreCase = true) -> "自定义服务 - 厂商特定功能"
                    uuid.startsWith("6E40", ignoreCase = true) -> "Nordic UART服务 - 串口通信功能"
                    uuid.startsWith("0000FFE", ignoreCase = true) -> "自定义通信服务 - 数据传输功能"
                    else -> ""
                }
            }
        }
    }
}