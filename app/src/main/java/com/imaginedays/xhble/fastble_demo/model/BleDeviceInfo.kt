package com.imaginedays.xhble.fastble_demo.model

import com.clj.fastble.data.BleDevice

/**
 * BLE设备信息数据类
 * 封装设备的基本信息和连接状态
 */
data class BleDeviceInfo(
    val bleDevice: BleDevice,
    val deviceName: String?,
    val macAddress: String,
    val rssi: Int,
    val isConnected: Boolean = false,
    val isConnecting: Boolean = false,
    val scanRecord: ByteArray? = null,
    val lastScanTime: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BleDeviceInfo

        if (macAddress != other.macAddress) return false

        return true
    }

    override fun hashCode(): Int {
        return macAddress.hashCode()
    }

    /**
     * 获取显示名称，优先使用设备名称，否则使用MAC地址
     */
    fun getDisplayName(): String {
        return if (!deviceName.isNullOrBlank()) {
            deviceName
        } else {
            macAddress
        }
    }

    /**
     * 获取信号强度描述
     */
    fun getSignalStrengthDescription(): String {
        return when {
            rssi >= -50 -> "强"
            rssi >= -70 -> "中"
            rssi >= -90 -> "弱"
            else -> "很弱"
        }
    }
}