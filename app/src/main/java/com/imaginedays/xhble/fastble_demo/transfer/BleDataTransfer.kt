package com.imaginedays.xhble.fastble_demo.transfer

import com.clj.fastble.BleManager
import com.clj.fastble.callback.BleMtuChangedCallback
import com.clj.fastble.callback.BleNotifyCallback
import com.clj.fastble.callback.BleReadCallback
import com.clj.fastble.callback.BleWriteCallback
import com.clj.fastble.data.BleDevice
import com.clj.fastble.exception.BleException
import com.imaginedays.xhble.fastble_demo.callback.BleDataCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import java.util.concurrent.ConcurrentHashMap

/**
 * BLE数据传输类
 * 负责处理数据的读取、写入和通知功能
 */
class BleDataTransfer {
    
    private val dataCallbacks = mutableListOf<BleDataCallback>()
    private val notificationStates = ConcurrentHashMap<String, Boolean>()
    
    /**
     * 写入数据到指定特征值
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 要写入的数据
     */
    fun writeData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        val bleDevice = deviceInfo.bleDevice
        
        if (!BleManager.getInstance().isConnected(bleDevice)) {
            notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        BleManager.getInstance().write(
            bleDevice,
            serviceUuid,
            characteristicUuid,
            data,
            object : BleWriteCallback() {
                override fun onWriteSuccess(current: Int, total: Int, justWrite: ByteArray?) {
                    notifyOnWriteSuccess(deviceInfo, serviceUuid, characteristicUuid, justWrite ?: data)
                }
                
                override fun onWriteFailure(exception: BleException?) {
                    val errorCode = exception?.code ?: -1
                    val errorMessage = exception?.description ?: "写入失败"
                    notifyOnWriteFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
                }
            }
        )
    }
    
    /**
     * 从指定特征值读取数据
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     */
    fun readData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String
    ) {
        val bleDevice = deviceInfo.bleDevice
        
        if (!BleManager.getInstance().isConnected(bleDevice)) {
            notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        BleManager.getInstance().read(
            bleDevice,
            serviceUuid,
            characteristicUuid,
            object : BleReadCallback() {
                override fun onReadSuccess(data: ByteArray?) {
                    data?.let {
                        notifyOnReadSuccess(deviceInfo, serviceUuid, characteristicUuid, it)
                    } ?: run {
                        notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "读取数据为空")
                    }
                }
                
                override fun onReadFailure(exception: BleException?) {
                    val errorCode = exception?.code ?: -1
                    val errorMessage = exception?.description ?: "读取失败"
                    notifyOnReadFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
                }
            }
        )
    }
    
    /**
     * 设置通知
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param enable 是否启用通知
     */
    fun setNotification(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        enable: Boolean
    ) {
        val bleDevice = deviceInfo.bleDevice
        
        if (!BleManager.getInstance().isConnected(bleDevice)) {
            notifyOnNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, -1, "设备未连接")
            return
        }
        
        val notificationKey = "${deviceInfo.macAddress}_${serviceUuid}_$characteristicUuid"
        
        if (enable) {
            // 启用通知
            BleManager.getInstance().notify(
                bleDevice,
                serviceUuid,
                characteristicUuid,
                object : BleNotifyCallback() {
                    override fun onNotifySuccess() {
                        notificationStates[notificationKey] = true
                        notifyOnNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, true)
                    }
                    
                    override fun onNotifyFailure(exception: BleException?) {
                        val errorCode = exception?.code ?: -1
                        val errorMessage = exception?.description ?: "启用通知失败"
                        notifyOnNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
                    }
                    
                    override fun onCharacteristicChanged(data: ByteArray?) {
                        data?.let {
                            notifyOnNotificationReceived(deviceInfo, serviceUuid, characteristicUuid, it)
                        }
                    }
                }
            )
        } else {
            // 停用通知
            BleManager.getInstance().stopNotify(bleDevice, serviceUuid, characteristicUuid)
            notificationStates[notificationKey] = false
            notifyOnNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, false)
        }
    }
    
    /**
     * 检查通知是否已启用
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     */
    fun isNotificationEnabled(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String
    ): Boolean {
        val notificationKey = "${deviceInfo.macAddress}_${serviceUuid}_$characteristicUuid"
        return notificationStates[notificationKey] ?: false
    }
    
    /**
     * 停用设备的所有通知
     * @param deviceInfo 设备信息
     */
    fun stopAllNotifications(deviceInfo: BleDeviceInfo) {
        val bleDevice = deviceInfo.bleDevice
        // 注意：stopNotify(bleDevice) 方法在FastBLE 2.4.0中可能不存在
        // 需要逐个停用每个通知
        val keysToRemove = notificationStates.keys.filter { it.startsWith(deviceInfo.macAddress) }
        keysToRemove.forEach { key ->
            val parts = key.split("_")
            if (parts.size >= 3) {
                val serviceUuid = parts[1]
                val characteristicUuid = parts[2]
                BleManager.getInstance().stopNotify(bleDevice, serviceUuid, characteristicUuid)
            }
            notificationStates.remove(key)
        }
    }
    
    /**
     * 设置MTU大小
     * @param deviceInfo 设备信息
     * @param mtu MTU大小
     * @param callback MTU变更回调
     */
    fun setMtu(deviceInfo: BleDeviceInfo, mtu: Int, callback: BleMtuChangedCallback) {
        BleManager.getInstance().setMtu(deviceInfo.bleDevice, mtu, callback)
    }
    
    /**
     * 添加数据传输回调
     */
    fun addDataCallback(callback: BleDataCallback) {
        if (!dataCallbacks.contains(callback)) {
            dataCallbacks.add(callback)
        }
    }
    
    /**
     * 移除数据传输回调
     */
    fun removeDataCallback(callback: BleDataCallback) {
        dataCallbacks.remove(callback)
    }
    
    /**
     * 移除所有数据传输回调
     */
    fun removeAllDataCallbacks() {
        dataCallbacks.clear()
    }
    
    /**
     * 清除所有通知状态
     */
    fun clearNotificationStates() {
        notificationStates.clear()
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnWriteSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onWriteSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnWriteFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onWriteFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnReadSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onReadSuccess(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnReadFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onReadFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationReceived(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationReceived(deviceInfo, serviceUuid, characteristicUuid, data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationSetSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        isEnabled: Boolean
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, isEnabled)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnNotificationSetFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    ) {
        dataCallbacks.forEach { callback ->
            try {
                callback.onNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}