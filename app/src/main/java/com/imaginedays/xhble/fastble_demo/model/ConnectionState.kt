package com.imaginedays.xhble.fastble_demo.model

/**
 * BLE连接状态枚举
 */
enum class ConnectionState {
    /** 未连接 */
    DISCONNECTED,
    
    /** 连接中 */
    CONNECTING,
    
    /** 已连接 */
    CONNECTED,
    
    /** 断开连接中 */
    DISCONNECTING,
    
    /** 连接失败 */
    CONNECT_FAILED,
    
    /** 扫描中 */
    SCANNING,
    
    /** 扫描停止 */
    SCAN_STOPPED;
    
    /**
     * 是否为连接状态
     */
    fun isConnected(): <PERSON><PERSON><PERSON> {
        return this == CONNECTED
    }
    
    /**
     * 是否为连接中状态
     */
    fun isConnecting(): <PERSON><PERSON><PERSON> {
        return this == CONNECTING
    }
    
    /**
     * 是否为断开状态
     */
    fun isDisconnected(): <PERSON><PERSON><PERSON> {
        return this == DISCONNECTED || this == CONNECT_FAILED
    }
    
    /**
     * 获取状态描述
     */
    fun getDescription(): String {
        return when (this) {
            DISCONNECTED -> "未连接"
            CONNECTING -> "连接中"
            CONNECTED -> "已连接"
            DISCONNECTING -> "断开连接中"
            CONNECT_FAILED -> "连接失败"
            SCANNING -> "扫描中"
            SCAN_STOPPED -> "扫描停止"
        }
    }
}