package com.imaginedays.xhble.fastble_demo.scanner

import com.clj.fastble.BleManager
import com.clj.fastble.callback.BleScanCallback
import com.clj.fastble.data.BleDevice
import com.clj.fastble.scan.BleScanRuleConfig
import com.imaginedays.xhble.fastble_demo.callback.BleScanCallback as CustomBleScanCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * BLE设备扫描器
 * 负责扫描周围的BLE设备
 */
class BleDeviceScanner {
    
    private var isScanning = false
    private val scanCallbacks = mutableListOf<CustomBleScanCallback>()
    private val scanResults = ConcurrentHashMap<String, BleDeviceInfo>()
    
    /**
     * 开始扫描设备
     * @param timeoutMillis 扫描超时时间（毫秒）
     * @param deviceNames 指定扫描的设备名称列表，为空则扫描所有设备
     * @param serviceUuids 指定扫描的服务UUID列表，为空则扫描所有服务
     */
    fun startScan(
        timeoutMillis: Long = 10000,
        deviceNames: Array<String>? = null,
        serviceUuids: Array<String>? = null
    ) {
        if (isScanning) {
            return
        }
        
        // 清空之前的扫描结果
        scanResults.clear()
        
        // 配置扫描规则
        val scanRuleConfig = BleScanRuleConfig.Builder().apply {
            // 设置设备名称过滤
            deviceNames?.let { names ->
                if (names.isNotEmpty()) {
                    setDeviceName(true, *names)
                }
            }
            
            // 设置服务UUID过滤
            serviceUuids?.let { uuids ->
                if (uuids.isNotEmpty()) {
                    val uuidArray = uuids.map { UUID.fromString(it) }.toTypedArray()
                    setServiceUuids(uuidArray)
                }
            }
            
            setAutoConnect(false)
            setScanTimeOut(timeoutMillis)
        }.build()
        
        BleManager.getInstance().initScanRule(scanRuleConfig)
        
        // 开始扫描
        BleManager.getInstance().scan(object : BleScanCallback() {
            override fun onScanStarted(success: Boolean) {
                isScanning = success
                if (success) {
                    notifyOnScanStarted()
                } else {
                    notifyOnScanFailed(-1, "扫描启动失败")
                }
            }
            
            override fun onLeScan(bleDevice: BleDevice?) {
                bleDevice?.let { device ->
                    // 只显示以"Hi-XH"开头的设备名称
                    device.name?.let { deviceName ->
//                        if (isXHDevice(deviceName)) {
                            val deviceInfo = createBleDeviceInfo(device)
                            scanResults[device.mac] = deviceInfo
                            notifyOnDeviceFound(deviceInfo)
//                        }
                    }
                }
            }
            
            override fun onScanning(bleDevice: BleDevice?) {
                bleDevice?.let { device ->
                    // 只显示以"Hi-XH"开头的设备名称
                    device.name?.let { deviceName ->
//                        if (isXHDevice(deviceName)) {
                            val deviceInfo = createBleDeviceInfo(device)
                            scanResults[device.mac] = deviceInfo
                            notifyOnDeviceFound(deviceInfo)
//                        }
                    }
                }
            }
            
            override fun onScanFinished(scanResultList: MutableList<BleDevice>?) {
                isScanning = false
                val results = scanResults.values.toList()
                notifyOnScanFinished(results)
            }
        })
    }

    fun isXHDevice(deviceName: String): Boolean {
        return deviceName.startsWith("Hi-XH")
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        if (isScanning) {
            BleManager.getInstance().cancelScan()
            isScanning = false
        }
    }
    
    /**
     * 是否正在扫描
     */
    fun isScanning(): Boolean {
        return isScanning
    }
    
    /**
     * 获取扫描结果
     */
    fun getScanResults(): List<BleDeviceInfo> {
        return scanResults.values.toList()
    }
    
    /**
     * 根据MAC地址获取设备信息
     */
    fun getDeviceByMac(macAddress: String): BleDeviceInfo? {
        return scanResults[macAddress]
    }
    
    /**
     * 清空扫描结果
     */
    fun clearScanResults() {
        scanResults.clear()
    }
    
    /**
     * 添加扫描回调
     */
    fun addScanCallback(callback: CustomBleScanCallback) {
        if (!scanCallbacks.contains(callback)) {
            scanCallbacks.add(callback)
        }
    }
    
    /**
     * 移除扫描回调
     */
    fun removeScanCallback(callback: CustomBleScanCallback) {
        scanCallbacks.remove(callback)
    }
    
    /**
     * 移除所有扫描回调
     */
    fun removeAllScanCallbacks() {
        scanCallbacks.clear()
    }
    
    /**
     * 创建BleDeviceInfo对象
     */
    private fun createBleDeviceInfo(bleDevice: BleDevice): BleDeviceInfo {
        return BleDeviceInfo(
            bleDevice = bleDevice,
            deviceName = bleDevice.name,
            macAddress = bleDevice.mac,
            rssi = bleDevice.rssi,
            isConnected = BleManager.getInstance().isConnected(bleDevice),
            isConnecting = false,
            scanRecord = bleDevice.scanRecord,
            lastScanTime = System.currentTimeMillis()
        )
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnScanStarted() {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanStarted()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnDeviceFound(deviceInfo: BleDeviceInfo) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onDeviceFound(deviceInfo)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnScanFinished(scanResults: List<BleDeviceInfo>) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanFinished(scanResults)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnScanFailed(errorCode: Int, errorMessage: String) {
        scanCallbacks.forEach { callback ->
            try {
                callback.onScanFailed(errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}