package com.imaginedays.xhble.fastble_demo.manager

import android.app.Application
import android.content.Context
import com.clj.fastble.BleManager
import com.clj.fastble.data.BleDevice
import com.imaginedays.xhble.fastble_demo.callback.BleScanCallback
import com.imaginedays.xhble.fastble_demo.callback.BleConnectionCallback
import com.imaginedays.xhble.fastble_demo.callback.BleDataCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.model.ConnectionState
import com.imaginedays.xhble.fastble_demo.scanner.BleDeviceScanner
import com.imaginedays.xhble.fastble_demo.connection.BleConnectionManager
import com.imaginedays.xhble.fastble_demo.transfer.BleDataTransfer

/**
 * FastBLE主管理类
 * 统一管理BLE的扫描、连接、数据传输等功能
 */
class FastBleManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: FastBleManager? = null
        
        fun getInstance(): FastBleManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FastBleManager().also { INSTANCE = it }
            }
        }
    }
    
    private var isInitialized = false
    private lateinit var deviceScanner: BleDeviceScanner
    private lateinit var connectionManager: BleConnectionManager
    private lateinit var dataTransfer: BleDataTransfer
    
    // 连接的设备列表
    private val connectedDevices = mutableMapOf<String, BleDeviceInfo>()
    
    // 全局回调监听器
    private val globalScanCallbacks = mutableListOf<BleScanCallback>()
    private val globalConnectionCallbacks = mutableListOf<BleConnectionCallback>()
    private val globalDataCallbacks = mutableListOf<BleDataCallback>()
    
    /**
     * 初始化FastBLE管理器
     * @param context 应用上下文
     */
    fun init(context: Context) {
        if (isInitialized) {
            return
        }
        
        // 初始化FastBLE库
        BleManager.getInstance().init(context.applicationContext as Application)
        BleManager.getInstance()
            .enableLog(true)
            .setReConnectCount(1, 5000)
            .setConnectOverTime(20000)
            .setOperateTimeout(5000)
        
        // 初始化各个管理器
        deviceScanner = BleDeviceScanner()
        connectionManager = BleConnectionManager()
        dataTransfer = BleDataTransfer()
        
        // 设置内部回调监听
        setupInternalCallbacks()
        
        isInitialized = true
    }
    
    /**
     * 设置内部回调监听
     */
    private fun setupInternalCallbacks() {
        // 扫描回调
        deviceScanner.addScanCallback(object : BleScanCallback {
            override fun onScanStarted() {
                globalScanCallbacks.forEach { it.onScanStarted() }
            }
            
            override fun onDeviceFound(deviceInfo: BleDeviceInfo) {
                globalScanCallbacks.forEach { it.onDeviceFound(deviceInfo) }
            }
            
            override fun onScanFinished(scanResults: List<BleDeviceInfo>) {
                globalScanCallbacks.forEach { it.onScanFinished(scanResults) }
            }
            
            override fun onScanFailed(errorCode: Int, errorMessage: String) {
                globalScanCallbacks.forEach { it.onScanFailed(errorCode, errorMessage) }
            }
        })
        
        // 连接回调
        connectionManager.addConnectionCallback(object : BleConnectionCallback {
            override fun onConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState) {
                updateDeviceConnectionState(deviceInfo, state)
                globalConnectionCallbacks.forEach { it.onConnectionStateChanged(deviceInfo, state) }
            }
            
            override fun onConnectSuccess(deviceInfo: BleDeviceInfo) {
                connectedDevices[deviceInfo.macAddress] = deviceInfo.copy(isConnected = true)
                globalConnectionCallbacks.forEach { it.onConnectSuccess(deviceInfo) }
            }
            
            override fun onConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {
                connectedDevices.remove(deviceInfo.macAddress)
                globalConnectionCallbacks.forEach { it.onConnectFailed(deviceInfo, errorCode, errorMessage) }
            }
            
            override fun onDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {
                connectedDevices.remove(deviceInfo.macAddress)
                globalConnectionCallbacks.forEach { it.onDisconnected(deviceInfo, isActiveDisconnect) }
            }
            
            override fun onServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
                globalConnectionCallbacks.forEach { it.onServicesDiscovered(deviceInfo, serviceUuids) }
            }
        })
        
        // 数据传输回调
        dataTransfer.addDataCallback(object : BleDataCallback {
            override fun onWriteSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
                globalDataCallbacks.forEach { it.onWriteSuccess(deviceInfo, serviceUuid, characteristicUuid, data) }
            }
            
            override fun onWriteFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
                globalDataCallbacks.forEach { it.onWriteFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage) }
            }
            
            override fun onReadSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
                globalDataCallbacks.forEach { it.onReadSuccess(deviceInfo, serviceUuid, characteristicUuid, data) }
            }
            
            override fun onReadFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
                globalDataCallbacks.forEach { it.onReadFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage) }
            }
            
            override fun onNotificationReceived(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, data: ByteArray) {
                globalDataCallbacks.forEach { it.onNotificationReceived(deviceInfo, serviceUuid, characteristicUuid, data) }
            }
            
            override fun onNotificationSetSuccess(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, isEnabled: Boolean) {
                globalDataCallbacks.forEach { it.onNotificationSetSuccess(deviceInfo, serviceUuid, characteristicUuid, isEnabled) }
            }
            
            override fun onNotificationSetFailed(deviceInfo: BleDeviceInfo, serviceUuid: String, characteristicUuid: String, errorCode: Int, errorMessage: String) {
                globalDataCallbacks.forEach { it.onNotificationSetFailed(deviceInfo, serviceUuid, characteristicUuid, errorCode, errorMessage) }
            }
        })
    }
    
    /**
     * 更新设备连接状态
     */
    private fun updateDeviceConnectionState(deviceInfo: BleDeviceInfo, state: ConnectionState) {
        when (state) {
            ConnectionState.CONNECTED -> {
                connectedDevices[deviceInfo.macAddress] = deviceInfo.copy(isConnected = true, isConnecting = false)
            }
            ConnectionState.CONNECTING -> {
                connectedDevices[deviceInfo.macAddress] = deviceInfo.copy(isConnected = false, isConnecting = true)
            }
            ConnectionState.DISCONNECTED, ConnectionState.CONNECT_FAILED -> {
                connectedDevices.remove(deviceInfo.macAddress)
            }
            else -> {}
        }
    }
    
    // ==================== 扫描相关方法 ====================
    
    /**
     * 开始扫描设备
     * @param timeoutMillis 扫描超时时间（毫秒）
     * @param deviceNames 指定扫描的设备名称列表，为空则扫描所有设备
     * @param serviceUuids 指定扫描的服务UUID列表，为空则扫描所有服务
     */
    fun startScan(
        timeoutMillis: Long = 10000,
        deviceNames: Array<String>? = null,
        serviceUuids: Array<String>? = null
    ) {
        checkInitialized()
        deviceScanner.startScan(timeoutMillis, deviceNames, serviceUuids)
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        checkInitialized()
        deviceScanner.stopScan()
    }
    
    /**
     * 是否正在扫描
     */
    fun isScanning(): Boolean {
        checkInitialized()
        return deviceScanner.isScanning()
    }
    
    /**
     * 获取扫描结果
     */
    fun getScanResults(): List<BleDeviceInfo> {
        checkInitialized()
        return deviceScanner.getScanResults()
    }
    
    // ==================== 连接相关方法 ====================
    
    /**
     * 连接设备
     * @param deviceInfo 设备信息
     * @param autoConnect 是否自动连接
     */
    fun connect(deviceInfo: BleDeviceInfo, autoConnect: Boolean = false) {
        checkInitialized()
        connectionManager.connect(deviceInfo, autoConnect)
    }
    
    /**
     * 断开设备连接
     * @param deviceInfo 设备信息
     */
    fun disconnect(deviceInfo: BleDeviceInfo) {
        checkInitialized()
        connectionManager.disconnect(deviceInfo)
    }
    
    /**
     * 断开所有设备连接
     */
    fun disconnectAll() {
        checkInitialized()
        connectionManager.disconnectAll()
        connectedDevices.clear()
    }
    
    /**
     * 获取已连接的设备列表
     */
    fun getConnectedDevices(): List<BleDeviceInfo> {
        return connectedDevices.values.toList()
    }
    
    /**
     * 检查设备是否已连接
     */
    fun isConnected(macAddress: String): Boolean {
        return connectedDevices.containsKey(macAddress)
    }
    
    // ==================== 数据传输相关方法 ====================
    
    /**
     * 写入数据
     */
    fun writeData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    ) {
        checkInitialized()
        dataTransfer.writeData(deviceInfo, serviceUuid, characteristicUuid, data)
    }
    
    /**
     * 读取数据
     */
    fun readData(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String
    ) {
        checkInitialized()
        dataTransfer.readData(deviceInfo, serviceUuid, characteristicUuid)
    }
    
    /**
     * 设置通知
     */
    fun setNotification(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        enable: Boolean
    ) {
        checkInitialized()
        dataTransfer.setNotification(deviceInfo, serviceUuid, characteristicUuid, enable)
    }
    
    // ==================== 回调管理方法 ====================
    
    /**
     * 添加扫描回调
     */
    fun addScanCallback(callback: BleScanCallback) {
        if (!globalScanCallbacks.contains(callback)) {
            globalScanCallbacks.add(callback)
        }
    }
    
    /**
     * 移除扫描回调
     */
    fun removeScanCallback(callback: BleScanCallback) {
        globalScanCallbacks.remove(callback)
    }
    
    /**
     * 添加连接回调
     */
    fun addConnectionCallback(callback: BleConnectionCallback) {
        if (!globalConnectionCallbacks.contains(callback)) {
            globalConnectionCallbacks.add(callback)
        }
    }
    
    /**
     * 移除连接回调
     */
    fun removeConnectionCallback(callback: BleConnectionCallback) {
        globalConnectionCallbacks.remove(callback)
    }
    
    /**
     * 添加数据传输回调
     */
    fun addDataCallback(callback: BleDataCallback) {
        if (!globalDataCallbacks.contains(callback)) {
            globalDataCallbacks.add(callback)
        }
    }
    
    /**
     * 移除数据传输回调
     */
    fun removeDataCallback(callback: BleDataCallback) {
        globalDataCallbacks.remove(callback)
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 检查是否已初始化
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("FastBleManager must be initialized before use")
        }
    }
    
    /**
     * 检查蓝牙是否可用
     */
    fun isBluetoothEnabled(): Boolean {
        return BleManager.getInstance().isBlueEnable
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        if (isInitialized) {
            stopScan()
            disconnectAll()
            globalScanCallbacks.clear()
            globalConnectionCallbacks.clear()
            globalDataCallbacks.clear()
            BleManager.getInstance().destroy()
            isInitialized = false
        }
    }
}