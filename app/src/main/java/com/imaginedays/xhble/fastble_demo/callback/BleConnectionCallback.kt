package com.imaginedays.xhble.fastble_demo.callback

import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.model.ConnectionState

/**
 * BLE设备连接回调接口
 */
interface BleConnectionCallback {
    
    /**
     * 连接状态改变
     * @param deviceInfo 设备信息
     * @param state 连接状态
     */
    fun onConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState)
    
    /**
     * 连接成功
     * @param deviceInfo 设备信息
     */
    fun onConnectSuccess(deviceInfo: BleDeviceInfo)
    
    /**
     * 连接失败
     * @param deviceInfo 设备信息
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String)
    
    /**
     * 断开连接
     * @param deviceInfo 设备信息
     * @param isActiveDisconnect 是否为主动断开
     */
    fun onDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean)
    
    /**
     * 服务发现完成
     * @param deviceInfo 设备信息
     * @param serviceUuids 发现的服务UUID列表
     */
    fun onServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>)
}