package com.imaginedays.xhble.fastble_demo.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.imaginedays.xhble.R
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo

/**
 * BLE设备列表适配器
 */
class BleDeviceAdapter(
    private val onDeviceClick: (BleDeviceInfo) -> Unit
) : RecyclerView.Adapter<BleDeviceAdapter.DeviceViewHolder>() {
    
    private val devices = mutableListOf<BleDeviceInfo>()
    private var onActionClickListener: ((BleDeviceInfo) -> Unit)? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_ble_device, parent, false)
        return DeviceViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        holder.bind(devices[position])
    }
    
    override fun getItemCount(): Int = devices.size
    
    /**
     * 添加设备
     */
    fun addDevice(device: BleDeviceInfo) {
        val existingIndex = devices.indexOfFirst { it.macAddress == device.macAddress }
        if (existingIndex >= 0) {
            devices[existingIndex] = device
            notifyItemChanged(existingIndex)
        } else {
            devices.add(device)
            notifyItemInserted(devices.size - 1)
        }
    }
    
    /**
     * 更新设备
     */
    fun updateDevice(device: BleDeviceInfo) {
        val index = devices.indexOfFirst { it.macAddress == device.macAddress }
        if (index >= 0) {
            devices[index] = device
            notifyItemChanged(index)
        }
    }
    
    /**
     * 更新设备列表
     */
    fun updateDevices(newDevices: List<BleDeviceInfo>) {
        devices.clear()
        devices.addAll(newDevices)
        notifyDataSetChanged()
    }
    
    /**
     * 清空设备列表
     */
    fun clearDevices() {
        devices.clear()
        notifyDataSetChanged()
    }
    
    /**
     * 设置操作按钮点击监听器
     */
    fun setOnActionClickListener(listener: (BleDeviceInfo) -> Unit) {
        onActionClickListener = listener
    }
    
    inner class DeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.card_device)
        private val tvDeviceName: TextView = itemView.findViewById(R.id.tv_device_name)
        private val tvMacAddress: TextView = itemView.findViewById(R.id.tv_mac_address)
        private val tvRssi: TextView = itemView.findViewById(R.id.tv_rssi)
        private val tvConnectionStatus: TextView = itemView.findViewById(R.id.tv_connection_status)
        private val btnDeviceAction: MaterialButton = itemView.findViewById(R.id.btn_device_action)
        private val tvOperationHint: TextView = itemView.findViewById(R.id.tv_operation_hint)
        
        fun bind(device: BleDeviceInfo) {
            // 设备名称
            tvDeviceName.text = device.getDisplayName()
            
            // MAC地址
            tvMacAddress.text = device.macAddress
            
            // 信号强度
            tvRssi.text = "${device.rssi} dBm (${device.getSignalStrengthDescription()})"
            
            // 连接状态
            val (statusText, statusColor) = when {
                device.isConnected -> "已连接" to R.color.green_500
                device.isConnecting -> "连接中..." to R.color.orange_500
                else -> "未连接" to R.color.gray_500
            }
            
            tvConnectionStatus.text = statusText
            tvConnectionStatus.setTextColor(
                ContextCompat.getColor(itemView.context, statusColor)
            )
            
            // 设置卡片背景色
            val cardBackgroundColor = when {
                device.isConnected -> R.color.green_50
                device.isConnecting -> R.color.orange_50
                else -> R.color.white
            }
            cardView.setCardBackgroundColor(
                ContextCompat.getColor(itemView.context, cardBackgroundColor)
            )
            
            // 根据连接状态控制操作按钮显示
            if (device.isConnected) {
                btnDeviceAction.visibility = View.VISIBLE
                tvOperationHint.text = "点击断开设备或操作按钮查看服务"
            } else {
                btnDeviceAction.visibility = View.GONE
                tvOperationHint.text = "点击连接设备"
            }
            
            // 点击事件
            itemView.setOnClickListener {
                onDeviceClick(device)
            }
            
            // 设置操作按钮点击事件
            btnDeviceAction.setOnClickListener {
                onActionClickListener?.invoke(device)
            }
        }
    }
}