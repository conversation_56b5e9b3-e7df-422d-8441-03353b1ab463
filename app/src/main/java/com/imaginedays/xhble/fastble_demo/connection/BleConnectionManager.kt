package com.imaginedays.xhble.fastble_demo.connection

import android.bluetooth.BluetoothGatt
import com.clj.fastble.BleManager
import com.clj.fastble.callback.BleGattCallback
import com.clj.fastble.data.BleDevice
import com.clj.fastble.exception.BleException
import com.imaginedays.xhble.fastble_demo.callback.BleConnectionCallback
import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo
import com.imaginedays.xhble.fastble_demo.model.ConnectionState
import java.util.concurrent.ConcurrentHashMap

/**
 * BLE连接管理器
 * 负责管理设备的连接、断开连接和连接状态监控
 */
class BleConnectionManager {
    
    private val connectionCallbacks = mutableListOf<BleConnectionCallback>()
    private val deviceConnectionStates = ConcurrentHashMap<String, ConnectionState>()
    
    /**
     * 连接设备
     * @param deviceInfo 设备信息
     * @param autoConnect 是否自动连接
     */
    fun connect(deviceInfo: BleDeviceInfo, autoConnect: Boolean = false) {
        val bleDevice = deviceInfo.bleDevice
        
        // 检查是否已经连接
        if (BleManager.getInstance().isConnected(bleDevice)) {
            notifyOnConnectSuccess(deviceInfo)
            return
        }
        
        // 检查是否正在连接
        if (deviceConnectionStates[deviceInfo.macAddress] == ConnectionState.CONNECTING) {
            return
        }
        
        // 更新连接状态
        updateConnectionState(deviceInfo, ConnectionState.CONNECTING)
        
        // 开始连接
        BleManager.getInstance().connect(bleDevice, object : BleGattCallback() {
            override fun onStartConnect() {
                updateConnectionState(deviceInfo, ConnectionState.CONNECTING)
            }
            
            override fun onConnectFail(bleDevice: BleDevice?, exception: BleException?) {
                val errorCode = exception?.code ?: -1
                val errorMessage = exception?.description ?: "连接失败"
                updateConnectionState(deviceInfo, ConnectionState.CONNECT_FAILED)
                notifyOnConnectFailed(deviceInfo, errorCode, errorMessage)
            }
            
            override fun onConnectSuccess(bleDevice: BleDevice?, gatt: BluetoothGatt?, status: Int) {
                updateConnectionState(deviceInfo, ConnectionState.CONNECTED)
                notifyOnConnectSuccess(deviceInfo)
                
                // 发现服务
                discoverServices(deviceInfo, gatt)
            }
            
            override fun onDisConnected(
                isActiveDisConnected: Boolean,
                bleDevice: BleDevice?,
                gatt: BluetoothGatt?,
                status: Int
            ) {
                updateConnectionState(deviceInfo, ConnectionState.DISCONNECTED)
                notifyOnDisconnected(deviceInfo, isActiveDisConnected)
            }
        })
    }
    
    /**
     * 断开设备连接
     * @param deviceInfo 设备信息
     */
    fun disconnect(deviceInfo: BleDeviceInfo) {
        val bleDevice = deviceInfo.bleDevice
        
        if (BleManager.getInstance().isConnected(bleDevice)) {
            updateConnectionState(deviceInfo, ConnectionState.DISCONNECTING)
            BleManager.getInstance().disconnect(bleDevice)
        }
    }
    
    /**
     * 断开所有设备连接
     */
    fun disconnectAll() {
        BleManager.getInstance().disconnectAllDevice()
        deviceConnectionStates.clear()
    }
    
    /**
     * 获取设备连接状态
     * @param macAddress 设备MAC地址
     */
    fun getConnectionState(macAddress: String): ConnectionState {
        return deviceConnectionStates[macAddress] ?: ConnectionState.DISCONNECTED
    }
    
    /**
     * 检查设备是否已连接
     * @param deviceInfo 设备信息
     */
    fun isConnected(deviceInfo: BleDeviceInfo): Boolean {
        return BleManager.getInstance().isConnected(deviceInfo.bleDevice)
    }
    
    /**
     * 检查设备是否正在连接
     * @param macAddress 设备MAC地址
     */
    fun isConnecting(macAddress: String): Boolean {
        return deviceConnectionStates[macAddress] == ConnectionState.CONNECTING
    }
    
    /**
     * 获取已连接的设备列表
     */
    fun getConnectedDevices(): List<BleDevice> {
        return BleManager.getInstance().allConnectedDevice
    }
    
    /**
     * 根据MAC地址获取已连接的设备
     */
    fun getConnectedDevice(macAddress: String): BleDevice? {
        return getConnectedDevices().find { it.mac == macAddress }
    }
    
    /**
     * 发现服务
     */
    private fun discoverServices(deviceInfo: BleDeviceInfo, gatt: BluetoothGatt?) {
        gatt?.let { bluetoothGatt ->
            val services = bluetoothGatt.services
            val serviceUuids = services.map { it.uuid.toString() }
            notifyOnServicesDiscovered(deviceInfo, serviceUuids)
        }
    }
    
    /**
     * 更新连接状态
     */
    private fun updateConnectionState(deviceInfo: BleDeviceInfo, state: ConnectionState) {
        deviceConnectionStates[deviceInfo.macAddress] = state
        notifyOnConnectionStateChanged(deviceInfo, state)
    }
    
    /**
     * 添加连接回调
     */
    fun addConnectionCallback(callback: BleConnectionCallback) {
        if (!connectionCallbacks.contains(callback)) {
            connectionCallbacks.add(callback)
        }
    }
    
    /**
     * 移除连接回调
     */
    fun removeConnectionCallback(callback: BleConnectionCallback) {
        connectionCallbacks.remove(callback)
    }
    
    /**
     * 移除所有连接回调
     */
    fun removeAllConnectionCallbacks() {
        connectionCallbacks.clear()
    }
    
    // ==================== 回调通知方法 ====================
    
    private fun notifyOnConnectionStateChanged(deviceInfo: BleDeviceInfo, state: ConnectionState) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectionStateChanged(deviceInfo, state)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnConnectSuccess(deviceInfo: BleDeviceInfo) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectSuccess(deviceInfo)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnConnectFailed(deviceInfo: BleDeviceInfo, errorCode: Int, errorMessage: String) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onConnectFailed(deviceInfo, errorCode, errorMessage)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnDisconnected(deviceInfo: BleDeviceInfo, isActiveDisconnect: Boolean) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onDisconnected(deviceInfo, isActiveDisconnect)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun notifyOnServicesDiscovered(deviceInfo: BleDeviceInfo, serviceUuids: List<String>) {
        connectionCallbacks.forEach { callback ->
            try {
                callback.onServicesDiscovered(deviceInfo, serviceUuids)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}