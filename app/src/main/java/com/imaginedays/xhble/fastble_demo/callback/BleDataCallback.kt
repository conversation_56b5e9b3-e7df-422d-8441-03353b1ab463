package com.imaginedays.xhble.fastble_demo.callback

import com.imaginedays.xhble.fastble_demo.model.BleDeviceInfo

/**
 * BLE数据传输回调接口
 */
interface BleDataCallback {
    
    /**
     * 数据写入成功
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 写入的数据
     */
    fun onWriteSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    )
    
    /**
     * 数据写入失败
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onWriteFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    )
    
    /**
     * 数据读取成功
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 读取的数据
     */
    fun onReadSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    )
    
    /**
     * 数据读取失败
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onReadFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    )
    
    /**
     * 接收到通知数据
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param data 通知数据
     */
    fun onNotificationReceived(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        data: ByteArray
    )
    
    /**
     * 通知设置成功
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param isEnabled 是否启用通知
     */
    fun onNotificationSetSuccess(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        isEnabled: Boolean
    )
    
    /**
     * 通知设置失败
     * @param deviceInfo 设备信息
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征值UUID
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onNotificationSetFailed(
        deviceInfo: BleDeviceInfo,
        serviceUuid: String,
        characteristicUuid: String,
        errorCode: Int,
        errorMessage: String
    )
}