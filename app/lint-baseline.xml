<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.4" type="baseline" client="gradle" dependencies="false" name="AGP (8.1.4)" variant="all" version="8.1.4">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            repository.disconnect()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/viewmodel/DeviceControlViewModel.kt"
            line="193"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="                            deviceStateManager.queryDeviceStatus()"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/utils/XH008BluetoothManager.kt"
            line="164"
            column="29"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return bluetoothManager.controlPower(true)"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/data/XH008Repository.kt"
            line="20"
            column="16"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return bluetoothManager.controlPower(false)"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/data/XH008Repository.kt"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return bluetoothManager.queryDeviceStatus()"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/data/XH008Repository.kt"
            line="28"
            column="16"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return bluetoothManager.connect(device)"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/data/XH008Repository.kt"
            line="33"
            column="16"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="        return bluetoothManager.setWorkMode(workMode)"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/data/XH008Repository.kt"
            line="42"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.fragment:fragment-ktx than 1.6.2 is available: 1.8.8"
        errorLine1="    implementation(libs.androidx.constraintlayout)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="54"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.2"
        errorLine1="    testImplementation(libs.junit)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="55"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.2"
        errorLine1="    androidTestImplementation(libs.androidx.junit)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="56"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.dagger:hilt-android than 2.48 is available: 2.53.1"
        errorLine1="    androidTestImplementation(libs.androidx.espresso.core)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="57"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.dagger:hilt-android-compiler than 2.48 is available: 2.53.1"
        errorLine1="    implementation(&quot;androidx.fragment:fragment-ktx:1.6.2&quot;)"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="58"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0"
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="$HOME/AndroidStudioProjects/xhble/gradle/libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="HardcodedDebugMode"
        message="Avoid hardcoding the debug mode; leaving it out allows debug and release builds to automatically assign one"
        errorLine1="        android:debuggable=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 31"
        errorLine1="            bluetoothGatt = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/utils/BluetoothConnectionManager.kt"
            line="59"
            column="33"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="This field leaks a context object"
        errorLine1="    @ApplicationContext private val applicationContext: Context"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/imaginedays/xhble/viewmodel/DeviceControlViewModel.kt"
            line="28"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(libs.androidx.constraintlayout)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="54"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    testImplementation(libs.junit)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="55"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    androidTestImplementation(libs.androidx.junit)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="56"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    androidTestImplementation(libs.androidx.espresso.core)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="57"
            column="21"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;androidx.fragment:fragment-ktx:1.6.2&quot;)"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="58"
            column="11"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_aromatherapy.xml"
            line="36"
            column="6"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;← 返回&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;← 返回&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_aromatherapy.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;设置&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;设置&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_aromatherapy.xml"
            line="32"
            column="9"/>
    </issue>

</issues>
